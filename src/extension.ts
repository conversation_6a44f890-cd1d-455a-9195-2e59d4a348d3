// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import { MainPanel } from './webviews/MainPanel';
import { NextEditPanel } from './webviews/NextEditPanel';
import { MonacoPanel } from './webviews/MonacoPanel';
import { RulesPanel } from './webviews/RulesPanel';
import { MemoryPanel } from './webviews/MemoryPanel';
import { FixPanel } from './webviews/FixPanel';
import { DiffPanel } from './webviews/DiffPanel';
import { FixViewProvider, FixTreeItem } from './webviews/FixViewProvider';
import { DiffViewProvider, DiffTreeItem } from './webviews/DiffViewProvider';
import { NextEditService } from './services/NextEditService';
import { MonacoService } from './services/MonacoService';
import { RulesService, RuleType } from './services/RulesService';
import { MemoryService } from './services/MemoryService';
import { DiagnosticService } from './services/DiagnosticService';
import { BatchFixService } from './services/BatchFixService';
import { DiffService } from './services/DiffService';

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {
	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	console.log('Congratulations, your extension "lazycode" is now active!');

	// 初始化服务
	const nextEditService = NextEditService.getInstance();
	const monacoService = MonacoService.getInstance();
	const rulesService = RulesService.getInstance();
	const memoryService = MemoryService.getInstance();
	const diagnosticService = DiagnosticService.getInstance();
	const batchFixService = BatchFixService.getInstance();
	
	// 初始化规则服务
	rulesService.initialize(context).catch(error => {
		console.error('初始化规则服务失败:', error);
	});
	
	// 初始化记忆服务
	memoryService.initialize(context).catch(error => {
		console.error('初始化记忆服务失败:', error);
	});
	
	// 初始化诊断服务
	try {
		diagnosticService.initialize(context);
	} catch (error: any) {
		console.error('初始化诊断服务失败:', error);
	}
	
	// 初始化批量修复服务
	try {
		batchFixService.initialize(context);
	} catch (error: any) {
		console.error('初始化批量修复服务失败:', error);
	}

	// 初始化差异比较服务
	try {
		const diffService = DiffService.getInstance();
		diffService.initialize(context);
	} catch (error: any) {
		console.error('初始化差异比较服务失败:', error);
	}

	// 注册Hello World命令
	const helloWorldCommand = vscode.commands.registerCommand('lazycode.helloWorld', () => {
		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from LazyCode!');
	});

	// 注册显示主面板的命令
	const showMainPanelCommand = vscode.commands.registerCommand('lazycode.showMainPanel', () => {
		MainPanel.render(context.extensionUri);
	});

	// 注册显示下一步编辑建议的命令
	const showNextEditCommand = vscode.commands.registerCommand('lazycode.showNextEdit', () => {
		// 显示编辑建议面板
		NextEditPanel.render(context.extensionUri);
	});

	// 注册刷新编辑建议的命令
	const refreshSuggestionsCommand = vscode.commands.registerCommand('lazycode.refreshSuggestions', async () => {
		await nextEditService.getSuggestions();
		vscode.window.showInformationMessage('已刷新编辑建议');
	});

	// 注册应用编辑建议的命令
	const applySuggestionCommand = vscode.commands.registerCommand('lazycode.applySuggestion', async (suggestionId: string) => {
		const result = await nextEditService.applySuggestion(suggestionId);
		if (result) {
			vscode.window.showInformationMessage('已应用编辑建议');
		} else {
			vscode.window.showErrorMessage('应用编辑建议失败');
		}
	});

	// 注册撤销最后一次编辑的命令
	const undoLastEditCommand = vscode.commands.registerCommand('lazycode.undoLastEdit', async () => {
		const result = await nextEditService.undoLastEdit();
		if (!result) {
			vscode.window.showErrorMessage('撤销编辑失败');
		}
	});

	// 注册显示Monaco编辑器的命令
	const showMonacoEditorCommand = vscode.commands.registerCommand('lazycode.showMonacoEditor', () => {
		// 显示Monaco编辑器面板
		MonacoPanel.render(context.extensionUri);
	});

	// 注册从活动编辑器加载内容的命令
	const loadFromActiveEditorCommand = vscode.commands.registerCommand('lazycode.loadFromActiveEditor', async () => {
		const config = await monacoService.loadFromActiveEditor();
		if (config) {
			vscode.window.showInformationMessage('已从活动编辑器加载内容');
		} else {
			vscode.window.showErrorMessage('从活动编辑器加载内容失败');
		}
	});
	
	// 注册显示规则面板的命令
	const showRulesPanelCommand = vscode.commands.registerCommand('lazycode.showRulesPanel', () => {
		// 显示规则面板
		RulesPanel.render(context.extensionUri);
	});
	
	// 注册创建新规则的命令
	const createRuleCommand = vscode.commands.registerCommand('lazycode.createRule', async () => {
		// 显示规则面板并初始化为创建模式
		const panel = RulesPanel.render(context.extensionUri);
		// 通知面板打开创建规则表单
		panel.postMessage({ command: 'openRuleEditor', isCreate: true });
	});
	
	// 注册刷新规则的命令
	const refreshRulesCommand = vscode.commands.registerCommand('lazycode.refreshRules', async () => {
		await rulesService.loadRules();
		vscode.window.showInformationMessage('已刷新规则');
	});

	// 注册应用规则到当前文件的命令
	const applyRuleCommand = vscode.commands.registerCommand('lazycode.applyRule', async (ruleId: string, ruleType?: RuleType) => {
		const editor = vscode.window.activeTextEditor;
		if (!editor) {
			vscode.window.showErrorMessage('没有活动的编辑器');
			return;
		}
		
		const rulesService = RulesService.getInstance();
		let rule;
		
		// 如果提供了规则ID，则使用指定的规则
		if (ruleId) {
			rule = rulesService.getRuleById(ruleId);
			if (!rule) {
				vscode.window.showErrorMessage(`未找到ID为 "${ruleId}" 的规则`);
				return;
			}
			
			// 获取文档内容和语言
			const document = editor.document;
			const content = document.getText();
			const language = document.languageId;
			
			// 应用单个规则
			try {
				const result = rulesService.applyRules(content, language, rule.type);
				
				// 如果内容有变化，则更新文档
				if (result !== content) {
					const fullRange = new vscode.Range(
						new vscode.Position(0, 0),
						new vscode.Position(document.lineCount, 0)
					);
					
					editor.edit(editBuilder => {
						editBuilder.replace(fullRange, result);
					});
					
					vscode.window.showInformationMessage(`已应用规则 "${rule.name}"`);
				} else {
					vscode.window.showInformationMessage(`规则 "${rule.name}" 没有产生变化`);
				}
			} catch (error) {
				vscode.window.showErrorMessage(`应用规则失败: ${error instanceof Error ? error.message : String(error)}`);
			}
		} 
		// 如果提供了规则类型，则应用该类型的所有规则
		else if (ruleType) {
			// 获取文档内容和语言
			const document = editor.document;
			const content = document.getText();
			const language = document.languageId;
			
			// 应用指定类型的规则
			try {
				const result = rulesService.applyRules(content, language, ruleType);
				
				// 如果内容有变化，则更新文档
				if (result !== content) {
					const fullRange = new vscode.Range(
						new vscode.Position(0, 0),
						new vscode.Position(document.lineCount, 0)
					);
					
					editor.edit(editBuilder => {
						editBuilder.replace(fullRange, result);
					});
					
					vscode.window.showInformationMessage(`已应用${getRuleTypeDisplayName(ruleType)}规则`);
				} else {
					vscode.window.showInformationMessage(`${getRuleTypeDisplayName(ruleType)}规则没有产生变化`);
				}
			} catch (error) {
				vscode.window.showErrorMessage(`应用规则失败: ${error instanceof Error ? error.message : String(error)}`);
			}
		}
		// 如果未提供规则ID和类型，则显示规则选择界面
		else {
			// 获取所有规则
			const rules = rulesService.getAllRules();
			if (rules.length === 0) {
				vscode.window.showInformationMessage('没有可用的规则');
				return;
			}
			
			// 创建规则快速选择项
			const items = rules.map(rule => ({
				label: rule.name,
				description: rule.description,
				detail: `类型: ${rule.type}, 语言: ${rule.languages.join(', ')}`,
				rule
			}));
			
			// 显示规则选择界面
			const selected = await vscode.window.showQuickPick(items, {
				placeHolder: '选择要应用的规则',
				matchOnDescription: true,
				matchOnDetail: true
			});
			
			if (selected) {
				// 递归调用自身，传入选择的规则ID
				vscode.commands.executeCommand('lazycode.applyRule', selected.rule.id);
			}
		}
	});
	
	// 注册应用所有规则到当前文件的命令
	const applyAllRulesCommand = vscode.commands.registerCommand('lazycode.applyAllRules', async () => {
		const editor = vscode.window.activeTextEditor;
		if (!editor) {
			vscode.window.showErrorMessage('没有活动的编辑器');
			return;
		}
		
		// 获取文档内容和语言
		const document = editor.document;
		const content = document.getText();
		const language = document.languageId;
		
		const rulesService = RulesService.getInstance();
		let result = content;
		
		// 依次应用各种类型的规则
		try {
			// 先应用分析规则
			result = rulesService.applyRules(result, language, RuleType.ANALYSIS);
			
			// 然后应用转换规则
			result = rulesService.applyRules(result, language, RuleType.TRANSFORMATION);
			
			// 最后应用生成规则
			result = rulesService.applyRules(result, language, RuleType.GENERATION);
			
			// 自定义规则可选择是否应用
			const applyCustom = await vscode.window.showQuickPick(['是', '否'], {
				placeHolder: '是否应用自定义规则？'
			});
			
			if (applyCustom === '是') {
				result = rulesService.applyRules(result, language, RuleType.CUSTOM);
			}
			
			// 如果内容有变化，则更新文档
			if (result !== content) {
				const fullRange = new vscode.Range(
					new vscode.Position(0, 0),
					new vscode.Position(document.lineCount, 0)
				);
				
				editor.edit(editBuilder => {
					editBuilder.replace(fullRange, result);
				});
				
				vscode.window.showInformationMessage('已应用所有规则');
			} else {
				vscode.window.showInformationMessage('应用规则后内容没有变化');
			}
		} catch (error) {
			vscode.window.showErrorMessage(`应用规则失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	});

	// 注册显示记忆面板的命令
	const showMemoryPanelCommand = vscode.commands.registerCommand('lazycode.showMemoryPanel', () => {
		// 显示记忆面板
		MemoryPanel.render(context.extensionUri);
	});
	
	// 注册捕获记忆的命令
	const captureMemoryCommand = vscode.commands.registerCommand('lazycode.captureMemory', async () => {
		// 显示输入框，让用户输入记忆名称
		const name = await vscode.window.showInputBox({
			prompt: '请输入记忆名称',
			placeHolder: '记忆名称'
		});
		
		if (!name) {
			return;
		}
		
		// 显示输入框，让用户输入记忆描述
		const description = await vscode.window.showInputBox({
			prompt: '请输入记忆描述',
			placeHolder: '记忆描述'
		});
		
		if (!description) {
			return;
		}
		
		// 捕获记忆
		const memory = await memoryService.captureFromEditor(name, description);
		
		if (memory) {
			vscode.window.showInformationMessage(`记忆 "${name}" 已捕获`);
		} else {
			vscode.window.showErrorMessage('捕获记忆失败，请确保编辑器中有内容或已选择内容');
		}
	});
	
	// 注册应用记忆的命令
	const applyMemoryCommand = vscode.commands.registerCommand('lazycode.applyMemory', async (memoryId?: string) => {
		// 如果未提供记忆ID，则显示快速选择界面
		if (!memoryId) {
			const memories = memoryService.getAllMemories();
			if (memories.length === 0) {
				vscode.window.showInformationMessage('没有可用的记忆');
				return;
			}
			
			// 创建记忆快速选择项
			const items = memories.map(memory => ({
				label: memory.name,
				description: memory.description,
				detail: `类型: ${memory.type}, 使用次数: ${memory.usageCount}`,
				memory
			}));
			
			// 显示记忆选择界面
			const selected = await vscode.window.showQuickPick(items, {
				placeHolder: '选择要应用的记忆',
				matchOnDescription: true,
				matchOnDetail: true
			});
			
			if (selected) {
				// 递归调用自身，传入选择的记忆ID
				vscode.commands.executeCommand('lazycode.applyMemory', selected.memory.id);
			}
			
			return;
		}
		
		// 应用记忆
		const result = await memoryService.applyToEditor(memoryId);
		
		// 显示结果消息
		const memory = memoryService.getMemoryById(memoryId);
		if (result && memory) {
			vscode.window.showInformationMessage(`已应用记忆 "${memory.name}"`);
		} else {
			vscode.window.showErrorMessage('应用记忆失败');
		}
	});

	// 注册显示修复面板的命令
	const showFixPanelCommand = vscode.commands.registerCommand('lazycode.showFixPanel', () => {
		// 显示修复面板
		FixPanel.render(context.extensionUri);
	});
	
	// 注册显示差异比较面板的命令
	const showDiffPanelCommand = vscode.commands.registerCommand('lazycode.showDiffPanel', () => {
		// 显示差异比较面板
		DiffPanel.render(context.extensionUri);
	});
	
	// 注册诊断当前文件的命令
	const diagnoseFileCommand = vscode.commands.registerCommand('lazycode.diagnoseFile', async () => {
		const editor = vscode.window.activeTextEditor;
		if (!editor) {
			vscode.window.showErrorMessage('没有活动的编辑器');
			return;
		}
		
		const document = editor.document;
		await diagnosticService.diagnoseDocument(document);
		vscode.window.showInformationMessage('已完成代码诊断');
	});

	// 注册代码修复视图提供者
	const fixViewProvider = new FixViewProvider(diagnosticService);
	context.subscriptions.push(
		vscode.window.registerTreeDataProvider('lazycode.fixView', fixViewProvider)
	);

	// 注册差异比较视图提供者
	const diffViewProvider = new DiffViewProvider(DiffService.getInstance());
	context.subscriptions.push(
		vscode.window.registerTreeDataProvider('lazycode.diffView', diffViewProvider)
	);

	// 注册跳转到问题位置的命令
	const gotoIssueLocationCommand = vscode.commands.registerCommand('lazycode.gotoIssueLocation', async (issueId: string) => {
		const issue = diagnosticService.getIssues().find(i => i.id === issueId);
		
		if (issue) {
			// 打开文件
			const document = await vscode.workspace.openTextDocument(issue.file);
			const editor = await vscode.window.showTextDocument(document);
			
			// 跳转到问题位置
			editor.selection = new vscode.Selection(issue.range.start, issue.range.end);
			
			// 滚动到可见区域
			editor.revealRange(issue.range, vscode.TextEditorRevealType.InCenter);
		} else {
			vscode.window.showErrorMessage('未找到指定的问题');
		}
	});

	// 添加所有命令到订阅列表
	context.subscriptions.push(
		helloWorldCommand,
		showMainPanelCommand,
		showNextEditCommand,
		refreshSuggestionsCommand,
		applySuggestionCommand,
		undoLastEditCommand,
		showMonacoEditorCommand,
		loadFromActiveEditorCommand,
		showRulesPanelCommand,
		createRuleCommand,
		refreshRulesCommand,
		applyRuleCommand,
		applyAllRulesCommand,
		showMemoryPanelCommand,
		captureMemoryCommand,
		applyMemoryCommand,
		showFixPanelCommand,
		diagnoseFileCommand,
		gotoIssueLocationCommand,
		showDiffPanelCommand
	);

	// 创建状态栏项
	const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
	statusBarItem.text = "$(lightbulb) LazyCode";
	statusBarItem.tooltip = "打开LazyCode主面板";
	statusBarItem.command = "lazycode.showMainPanel";
	statusBarItem.show();
	context.subscriptions.push(statusBarItem);
}

// This method is called when your extension is deactivated
export function deactivate() {}

/**
 * 获取规则类型显示名称
 * @param type 规则类型
 */
function getRuleTypeDisplayName(type: RuleType): string {
	switch (type) {
		case RuleType.GENERATION:
			return '代码生成';
		case RuleType.ANALYSIS:
			return '代码分析';
		case RuleType.TRANSFORMATION:
			return '代码转换';
		case RuleType.CUSTOM:
			return '自定义';
		default:
			return '未知';
	}
}
