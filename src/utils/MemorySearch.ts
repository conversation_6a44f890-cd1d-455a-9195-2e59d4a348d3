import { MemoryItem } from '../services/MemoryService';

/**
 * 记忆搜索选项接口
 */
export interface MemorySearchOptions {
  /**
   * 搜索关键词
   */
  query?: string;
  
  /**
   * 记忆类型过滤
   */
  type?: string;
  
  /**
   * 编程语言过滤
   */
  language?: string;
  
  /**
   * 标签过滤
   */
  tags?: string[];
  
  /**
   * 是否只显示星标记忆
   */
  onlyStarred?: boolean;
  
  /**
   * 排序方式
   */
  sortBy?: 'name' | 'lastUsed' | 'usage' | 'created' | 'relevance';
  
  /**
   * 排序顺序
   */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 高级记忆搜索工具类
 */
export class MemorySearch {
  /**
   * 搜索记忆
   * @param memories 记忆列表
   * @param options 搜索选项
   * @returns 搜索结果
   */
  public static search(memories: MemoryItem[], options: MemorySearchOptions): MemoryItem[] {
    let results = [...memories];
    
    // 应用各种过滤条件
    results = this.applyFilters(results, options);
    
    // 应用排序
    results = this.applySorting(results, options);
    
    return results;
  }
  
  /**
   * 应用过滤条件
   * @param memories 记忆列表
   * @param options 搜索选项
   * @returns 过滤后的记忆列表
   */
  private static applyFilters(memories: MemoryItem[], options: MemorySearchOptions): MemoryItem[] {
    let results = [...memories];
    
    // 关键词搜索
    if (options.query && options.query.trim() !== '') {
      const lowercaseQuery = options.query.toLowerCase();
      results = results.filter(memory => {
        return (
          memory.name.toLowerCase().includes(lowercaseQuery) ||
          memory.description.toLowerCase().includes(lowercaseQuery) ||
          memory.content.toLowerCase().includes(lowercaseQuery) ||
          memory.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
        );
      });
    }
    
    // 类型过滤
    if (options.type && options.type !== 'all') {
      results = results.filter(memory => memory.type === options.type);
    }
    
    // 语言过滤
    if (options.language) {
      results = results.filter(memory => 
        memory.languages.length === 0 || 
        memory.languages.includes(options.language!)
      );
    }
    
    // 标签过滤
    if (options.tags && options.tags.length > 0) {
      results = results.filter(memory => 
        options.tags!.some(tag => memory.tags.includes(tag))
      );
    }
    
    // 星标过滤
    if (options.onlyStarred) {
      results = results.filter(memory => memory.isStarred);
    }
    
    return results;
  }
  
  /**
   * 应用排序
   * @param memories 记忆列表
   * @param options 搜索选项
   * @returns 排序后的记忆列表
   */
  private static applySorting(memories: MemoryItem[], options: MemorySearchOptions): MemoryItem[] {
    const { sortBy = 'lastUsed', sortOrder = 'desc' } = options;
    
    return [...memories].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'lastUsed':
          comparison = a.lastUsedAt - b.lastUsedAt;
          break;
        case 'usage':
          comparison = a.usageCount - b.usageCount;
          break;
        case 'created':
          comparison = a.createdAt - b.createdAt;
          break;
      }
      
      // 调整排序顺序
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }
  
  /**
   * 根据相关性对记忆进行排序
   * @param memories 记忆列表
   * @param query 搜索关键词
   * @returns 排序后的记忆列表
   */
  public static sortByRelevance(memories: MemoryItem[], query: string): MemoryItem[] {
    if (!query || query.trim() === '') {
      return memories;
    }
    
    const lowercaseQuery = query.toLowerCase();
    
    // 计算每个记忆的相关性得分
    const scoredMemories = memories.map(memory => {
      let score = 0;
      
      // 标题匹配得分最高
      if (memory.name.toLowerCase().includes(lowercaseQuery)) {
        score += 100;
        // 如果是精确匹配，额外加分
        if (memory.name.toLowerCase() === lowercaseQuery) {
          score += 50;
        }
      }
      
      // 描述匹配
      if (memory.description.toLowerCase().includes(lowercaseQuery)) {
        score += 50;
      }
      
      // 内容匹配
      if (memory.content.toLowerCase().includes(lowercaseQuery)) {
        score += 25;
      }
      
      // 标签匹配
      if (memory.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))) {
        score += 75;
      }
      
      // 使用频率增加相关性
      score += memory.usageCount * 2;
      
      // 星标记忆优先
      if (memory.isStarred) {
        score += 30;
      }
      
      return { memory, score };
    });
    
    // 根据得分排序
    scoredMemories.sort((a, b) => b.score - a.score);
    
    // 返回排序后的记忆列表
    return scoredMemories.map(item => item.memory);
  }
  
  /**
   * 提取常用标签
   * @param memories 记忆列表
   * @param limit 限制数量
   * @returns 标签及其使用次数
   */
  public static extractCommonTags(memories: MemoryItem[], limit: number = 10): {tag: string, count: number}[] {
    // 统计标签使用次数
    const tagCounts: Record<string, number> = {};
    
    memories.forEach(memory => {
      memory.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });
    
    // 转换为数组并排序
    const sortedTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
    
    return sortedTags;
  }
  
  /**
   * 提取使用的编程语言
   * @param memories 记忆列表
   * @returns 语言及其使用次数
   */
  public static extractLanguages(memories: MemoryItem[]): {language: string, count: number}[] {
    // 统计语言使用次数
    const languageCounts: Record<string, number> = {};
    
    memories.forEach(memory => {
      memory.languages.forEach(language => {
        languageCounts[language] = (languageCounts[language] || 0) + 1;
      });
    });
    
    // 转换为数组并排序
    const sortedLanguages = Object.entries(languageCounts)
      .map(([language, count]) => ({ language, count }))
      .sort((a, b) => b.count - a.count);
    
    return sortedLanguages;
  }
} 