import * as vscode from 'vscode';
import { DiagnosticService, DiagnosticIssue, DiagnosticSeverity, DiagnosticSource } from '../services/DiagnosticService';

/**
 * 修复视图项目类
 * 用于在树视图中显示不同级别的项目
 */
export class FixTreeItem extends vscode.TreeItem {
  /**
   * 构造函数
   * @param label 标签
   * @param collapsibleState 可折叠状态
   * @param issue 关联的问题（可选）
   */
  constructor(
    public readonly label: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState,
    public readonly issue?: DiagnosticIssue
  ) {
    super(label, collapsibleState);
    
    // 设置基本属性
    this.tooltip = this.label;
    
    // 如果有关联的问题，设置问题相关属性
    if (issue) {
      this.description = issue.file.fsPath.split('/').pop();
      this.tooltip = `${issue.message} (${issue.file.fsPath})`;
      
      // 根据问题严重性设置图标
      switch (issue.severity) {
        case DiagnosticSeverity.ERROR:
          this.iconPath = new vscode.ThemeIcon('error');
          break;
        case DiagnosticSeverity.WARNING:
          this.iconPath = new vscode.ThemeIcon('warning');
          break;
        case DiagnosticSeverity.HINT:
          this.iconPath = new vscode.ThemeIcon('lightbulb');
          break;
        case DiagnosticSeverity.INFO:
          this.iconPath = new vscode.ThemeIcon('info');
          break;
      }
      
      // 设置命令，点击时跳转到问题位置
      this.command = {
        command: 'lazycode.gotoIssueLocation',
        title: '跳转到问题位置',
        arguments: [issue.id]
      };
    }
  }
  
  /**
   * 获取上下文值，用于右键菜单
   */
  contextValue = 'fixItem';
}

/**
 * 修复视图提供者类
 * 用于在侧边栏展示代码问题及修复功能
 */
export class FixViewProvider implements vscode.TreeDataProvider<FixTreeItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<FixTreeItem | undefined | null | void> = new vscode.EventEmitter<FixTreeItem | undefined | null | void>();
  readonly onDidChangeTreeData: vscode.Event<FixTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

  /**
   * 构造函数
   * @param diagnosticService 诊断服务
   */
  constructor(private diagnosticService: DiagnosticService) {
    // 监听诊断变化事件
    this.diagnosticService.onDiagnosticsChanged(() => {
      this.refresh();
    });
    
    // 监听编辑器变化
    vscode.window.onDidChangeActiveTextEditor(() => {
      this.refresh();
    });
  }

  /**
   * 刷新视图
   */
  refresh(): void {
    this._onDidChangeTreeData.fire();
  }

  /**
   * 获取树项
   * @param element 树元素
   */
  getTreeItem(element: FixTreeItem): vscode.TreeItem {
    return element;
  }

  /**
   * 获取子元素
   * @param element 父元素
   */
  getChildren(element?: FixTreeItem): Thenable<FixTreeItem[]> {
    if (!vscode.window.activeTextEditor) {
      return Promise.resolve([
        new FixTreeItem('没有打开的编辑器', vscode.TreeItemCollapsibleState.None)
      ]);
    }

    // 获取当前文件的所有问题
    const uri = vscode.window.activeTextEditor.document.uri;
    const issues = this.diagnosticService.getIssues(uri);
    
    // 如果没有找到问题
    if (issues.length === 0) {
      return Promise.resolve([
        new FixTreeItem('没有发现问题', vscode.TreeItemCollapsibleState.None)
      ]);
    }
    
    // 根级别
    if (!element) {
      // 按严重性分组
      const errors = issues.filter(issue => issue.severity === DiagnosticSeverity.ERROR);
      const warnings = issues.filter(issue => issue.severity === DiagnosticSeverity.WARNING);
      const hints = issues.filter(issue => issue.severity === DiagnosticSeverity.HINT);
      const infos = issues.filter(issue => issue.severity === DiagnosticSeverity.INFO);
      
      const result: FixTreeItem[] = [];
      
      if (errors.length > 0) {
        result.push(new FixTreeItem(`错误 (${errors.length})`, vscode.TreeItemCollapsibleState.Expanded));
      }
      
      if (warnings.length > 0) {
        result.push(new FixTreeItem(`警告 (${warnings.length})`, vscode.TreeItemCollapsibleState.Expanded));
      }
      
      if (hints.length > 0) {
        result.push(new FixTreeItem(`提示 (${hints.length})`, vscode.TreeItemCollapsibleState.Expanded));
      }
      
      if (infos.length > 0) {
        result.push(new FixTreeItem(`信息 (${infos.length})`, vscode.TreeItemCollapsibleState.Collapsed));
      }
      
      return Promise.resolve(result);
    } else {
      // 分类级别
      const label = element.label;
      let type: DiagnosticSeverity | undefined;
      
      if (label.startsWith('错误')) {
        type = DiagnosticSeverity.ERROR;
      } else if (label.startsWith('警告')) {
        type = DiagnosticSeverity.WARNING;
      } else if (label.startsWith('提示')) {
        type = DiagnosticSeverity.HINT;
      } else if (label.startsWith('信息')) {
        type = DiagnosticSeverity.INFO;
      }
      
      if (type) {
        const filteredIssues = issues.filter(issue => issue.severity === type);
        return Promise.resolve(
          filteredIssues.map(issue => 
            new FixTreeItem(issue.message, vscode.TreeItemCollapsibleState.None, issue)
          )
        );
      }
      
      return Promise.resolve([]);
    }
  }
} 