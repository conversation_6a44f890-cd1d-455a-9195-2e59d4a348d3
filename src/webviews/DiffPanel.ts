import * as vscode from 'vscode';
import { WebViewProvider } from './WebViewProvider';
import { DiffService, DiffResult, DiffMode, DiffType } from '../services/DiffService';

/**
 * 差异视图面板类
 */
export class DiffPanel extends WebViewProvider {
  private static instance: DiffPanel | undefined;
  private static readonly viewType = 'lazycode.diffPanel';
  private _diffResult: DiffResult | undefined;
  private _diffMode: DiffMode = DiffMode.LINE;

  /**
   * 获取单例实例
   */
  public static getInstance(extensionUri: vscode.Uri): DiffPanel {
    if (!DiffPanel.instance) {
      DiffPanel.instance = new DiffPanel(extensionUri);
    }
    return DiffPanel.instance;
  }

  /**
   * 创建或显示面板
   */
  public static render(extensionUri: vscode.Uri): DiffPanel {
    const panel = DiffPanel.getInstance(extensionUri);
    panel.reveal();
    return panel;
  }

  /**
   * 私有构造函数，用于创建差异视图面板
   */
  private constructor(extensionUri: vscode.Uri) {
    super(extensionUri, DiffPanel.viewType, '代码差异比较', vscode.ViewColumn.Two);
    
    // 监听差异生成事件
    const diffService = DiffService.getInstance();
    
    diffService.onDiffGenerated(result => {
      this._diffResult = result;
      this._diffMode = result.mode;
      this.updateContent();
    });
  }

  /**
   * 显示差异比较结果
   * @param diffResult 差异比较结果
   */
  public showDiffResult(diffResult: DiffResult): void {
    this._diffResult = diffResult;
    this._diffMode = diffResult.mode;
    this.updateContent();
  }

  /**
   * 更新面板内容
   */
  private updateContent(): void {
    if (this._panel && this._panel.webview) {
      this._panel.webview.html = this._getHtmlForWebview();
    }
  }

  /**
   * 获取WebView的HTML内容
   */
  protected _getHtmlForWebview(): string {
    // 差异结果HTML
    let diffContent = '';
    
    if (this._diffResult) {
      const { chunks, stats } = this._diffResult;
      
      // 添加统计信息
      diffContent += `
        <div class="diff-stats">
          <span class="added">添加: ${stats.added} 行</span>
          <span class="removed">删除: ${stats.removed} 行</span>
          <span class="unchanged">未变更: ${stats.unchanged} 行</span>
        </div>
      `;
      
      // 添加差异模式选择器
      diffContent += `
        <div class="diff-mode-selector">
          <label>比较模式: </label>
          <select id="diffMode">
            <option value="line" ${this._diffMode === DiffMode.LINE ? 'selected' : ''}>按行比较</option>
            <option value="word" ${this._diffMode === DiffMode.WORD ? 'selected' : ''}>按单词比较</option>
            <option value="character" ${this._diffMode === DiffMode.CHARACTER ? 'selected' : ''}>按字符比较</option>
          </select>
        </div>
      `;
      
      // 添加差异内容表格
      if (this._diffMode === DiffMode.LINE) {
        // 行比较模式使用表格显示
        diffContent += `
          <div class="diff-container">
            <table class="diff-table">
              <thead>
                <tr>
                  <th class="line-number">旧版本</th>
                  <th class="line-number">新版本</th>
                  <th class="code">代码</th>
                </tr>
              </thead>
              <tbody>
        `;
        
        // 处理每个差异块
        for (const chunk of chunks) {
          const { type, content, lineInfo } = chunk;
          
          // 对内容按行分割
          const lines = content.split('\n');
          
          // 如果最后一行为空，则移除它（这是由于split产生的）
          if (lines[lines.length - 1] === '' && content.endsWith('\n')) {
            lines.pop();
          }
          
          // 添加每一行到表格中
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            let oldLineNumber = '';
            let newLineNumber = '';
            
            if (type === DiffType.ADDED) {
              newLineNumber = lineInfo?.newStart !== undefined ? (lineInfo.newStart + i).toString() : '';
              diffContent += `
                <tr class="added">
                  <td class="line-number"></td>
                  <td class="line-number">${newLineNumber}</td>
                  <td class="code">${this.escapeHtml(line)}</td>
                </tr>
              `;
            } else if (type === DiffType.REMOVED) {
              oldLineNumber = lineInfo?.oldStart !== undefined ? (lineInfo.oldStart + i).toString() : '';
              diffContent += `
                <tr class="removed">
                  <td class="line-number">${oldLineNumber}</td>
                  <td class="line-number"></td>
                  <td class="code">${this.escapeHtml(line)}</td>
                </tr>
              `;
            } else {
              oldLineNumber = lineInfo?.oldStart !== undefined ? (lineInfo.oldStart + i).toString() : '';
              newLineNumber = lineInfo?.newStart !== undefined ? (lineInfo.newStart + i).toString() : '';
              diffContent += `
                <tr>
                  <td class="line-number">${oldLineNumber}</td>
                  <td class="line-number">${newLineNumber}</td>
                  <td class="code">${this.escapeHtml(line)}</td>
                </tr>
              `;
            }
          }
        }
        
        diffContent += `
              </tbody>
            </table>
          </div>
        `;
      } else {
        // 单词或字符比较模式使用内联显示
        diffContent += `<div class="diff-inline">`;
        
        for (const chunk of chunks) {
          const { type, content } = chunk;
          
          if (type === DiffType.ADDED) {
            diffContent += `<span class="added">${this.escapeHtml(content)}</span>`;
          } else if (type === DiffType.REMOVED) {
            diffContent += `<span class="removed">${this.escapeHtml(content)}</span>`;
          } else {
            diffContent += `<span>${this.escapeHtml(content)}</span>`;
          }
        }
        
        diffContent += `</div>`;
      }
    } else {
      // 没有差异结果时显示提示信息
      diffContent = `
        <div class="empty-state">
          <p>没有差异比较结果。</p>
          <p>请使用以下选项进行比较：</p>
          <div class="actions">
            <button id="compareWithClipboard">与剪贴板比较</button>
            <button id="compareWithPrevious">与上一版本比较</button>
            <button id="compareFiles">比较两个文件</button>
          </div>
        </div>
      `;
    }

    // 为差异视图面板创建HTML内容
    const bodyContent = `
    <div class="container">
      <header>
        <h1>代码差异比较</h1>
      </header>
      <main>
        ${diffContent}
      </main>
      <footer>
        <div class="actions">
          <button id="compareWithClipboard">与剪贴板比较</button>
          <button id="compareWithPrevious">与上一版本比较</button>
          <button id="compareFiles">比较两个文件</button>
        </div>
      </footer>
    </div>
    `;

    return this.generateHtmlWithInlineStyle("差异比较", bodyContent, this.getCssStyles(), []);
  }

  /**
   * 处理接收到的消息
   * @param message 消息对象
   */
  protected _onDidReceiveMessage(message: any): void {
    const diffService = DiffService.getInstance();
    
    switch (message.command) {
      case 'compareWithClipboard':
        diffService.compareWithClipboard().then(result => {
          if (result) {
            this.showDiffResult(result);
          }
        });
        break;
        
      case 'compareWithPrevious':
        diffService.compareWithPrevious().then(result => {
          if (result) {
            this.showDiffResult(result);
          }
        });
        break;
        
      case 'compareFiles':
        diffService.compareFiles().then(result => {
          if (result) {
            this.showDiffResult(result);
          }
        });
        break;
        
      case 'changeDiffMode':
        if (this._diffResult && message.mode) {
          const mode = message.mode as DiffMode;
          const result = diffService.compareText(this._diffResult.oldText, this._diffResult.newText, mode);
          this.showDiffResult(result);
        }
        break;
    }
  }

  /**
   * 获取CSS样式
   */
  private getCssStyles(): string {
    return `
      .container {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }
      
      header {
        padding: 10px;
        border-bottom: 1px solid var(--vscode-editorWidget-border);
      }
      
      main {
        flex: 1;
        overflow: auto;
        padding: 10px;
      }
      
      footer {
        padding: 10px;
        border-top: 1px solid var(--vscode-editorWidget-border);
      }
      
      .diff-stats {
        margin-bottom: 10px;
        padding: 5px;
        background-color: var(--vscode-editor-background);
        border-radius: 3px;
      }
      
      .diff-stats span {
        margin-right: 15px;
      }
      
      .diff-stats .added {
        color: var(--vscode-gitDecoration-addedResourceForeground);
      }
      
      .diff-stats .removed {
        color: var(--vscode-gitDecoration-deletedResourceForeground);
      }
      
      .diff-mode-selector {
        margin-bottom: 15px;
      }
      
      .diff-container {
        overflow: auto;
      }
      
      .diff-table {
        border-collapse: collapse;
        width: 100%;
        font-family: var(--vscode-editor-font-family);
        font-size: var(--vscode-editor-font-size);
      }
      
      .diff-table th, .diff-table td {
        padding: 2px 5px;
        text-align: left;
        border: 1px solid var(--vscode-editorWidget-border);
      }
      
      .diff-table .line-number {
        width: 40px;
        text-align: right;
        color: var(--vscode-editorLineNumber-foreground);
        background-color: var(--vscode-editor-background);
        padding-right: 10px;
        user-select: none;
      }
      
      .diff-table .code {
        white-space: pre;
        font-family: monospace;
      }
      
      .diff-table tr.added {
        background-color: var(--vscode-diffEditor-insertedTextBackground);
      }
      
      .diff-table tr.removed {
        background-color: var(--vscode-diffEditor-removedTextBackground);
      }
      
      .diff-inline {
        white-space: pre-wrap;
        font-family: monospace;
        line-height: 1.5;
      }
      
      .diff-inline .added {
        background-color: var(--vscode-diffEditor-insertedTextBackground);
        color: var(--vscode-diffEditor-insertedTextColor);
      }
      
      .diff-inline .removed {
        background-color: var(--vscode-diffEditor-removedTextBackground);
        color: var(--vscode-diffEditor-removedTextColor);
        text-decoration: line-through;
      }
      
      .empty-state {
        text-align: center;
        padding: 20px;
      }
      
      .actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 10px;
      }
      
      button {
        background-color: var(--vscode-button-background);
        color: var(--vscode-button-foreground);
        border: none;
        padding: 6px 12px;
        border-radius: 2px;
        cursor: pointer;
      }
      
      button:hover {
        background-color: var(--vscode-button-hoverBackground);
      }
      
      select {
        background-color: var(--vscode-dropdown-background);
        color: var(--vscode-dropdown-foreground);
        border: 1px solid var(--vscode-dropdown-border);
        padding: 4px 8px;
        border-radius: 2px;
      }
    `;
  }

  /**
   * 转义HTML字符
   * @param text 要转义的文本
   */
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }
} 