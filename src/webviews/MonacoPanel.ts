import * as vscode from 'vscode';
import { WebViewProvider } from './WebViewProvider';
import { MonacoService, MonacoEditorConfig } from '../services/MonacoService';

/**
 * Monaco编辑器面板类
 */
export class MonacoPanel extends WebViewProvider {
  private static instance: MonacoPanel | undefined;
  private static readonly viewType = 'lazycode.monacoPanel';
  private _config: MonacoEditorConfig;
  
  /**
   * 获取单例实例
   */
  public static getInstance(extensionUri: vscode.Uri): MonacoPanel {
    if (!MonacoPanel.instance) {
      MonacoPanel.instance = new MonacoPanel(extensionUri);
    }
    return MonacoPanel.instance;
  }
  
  /**
   * 创建或显示面板
   */
  public static render(extensionUri: vscode.Uri): MonacoPanel {
    const panel = MonacoPanel.getInstance(extensionUri);
    panel.reveal();
    
    // 从当前编辑器加载内容
    panel.loadFromActiveEditor();
    
    return panel;
  }
  
  /**
   * 私有构造函数，用于创建Monaco编辑器面板
   */
  private constructor(extensionUri: vscode.Uri) {
    super(extensionUri, MonacoPanel.viewType, 'Monaco编辑器', vscode.ViewColumn.One);
    
    // 获取Monaco服务
    const monacoService = MonacoService.getInstance();
    this._config = monacoService.getCurrentConfig();
    
    // 监听配置变化
    monacoService.onConfigChanged(config => {
      this._config = config;
      this.updateContent();
    });
  }
  
  /**
   * 从活动编辑器加载内容
   */
  private async loadFromActiveEditor(): Promise<void> {
    const monacoService = MonacoService.getInstance();
    await monacoService.loadFromActiveEditor();
  }
  
  /**
   * 更新面板内容
   */
  private updateContent(): void {
    if (this._panel && this._panel.webview) {
      this._panel.webview.html = this._getHtmlForWebview();
    }
  }
  
  /**
   * 获取WebView的HTML内容
   */
  protected _getHtmlForWebview(): string {
    // 获取Monaco服务和加载脚本
    const monacoService = MonacoService.getInstance();
    const monacoLoaderScript = monacoService.getMonacoLoaderScript();
    
    // 为Monaco编辑器面板创建HTML内容
    const bodyContent = `
    <div class="container">
      <header>
        <h1>Monaco编辑器</h1>
        <div class="header-actions">
          <button id="applyChanges" title="应用更改">应用更改</button>
          <button id="refreshContent" title="刷新内容">刷新</button>
          <select id="languageSelector" title="选择语言">
            <option value="plaintext">纯文本</option>
            <option value="javascript">JavaScript</option>
            <option value="typescript">TypeScript</option>
            <option value="html">HTML</option>
            <option value="css">CSS</option>
            <option value="json">JSON</option>
            <option value="markdown">Markdown</option>
            <option value="python">Python</option>
            <option value="java">Java</option>
            <option value="csharp">C#</option>
            <option value="cpp">C++</option>
          </select>
        </div>
      </header>
      <main>
        <div id="editor-container"></div>
      </main>
      <footer>
        <div class="status-bar">
          <div id="language-status">语言: ${this._config.language}</div>
          <div id="position-status">行: 1, 列: 1</div>
          <div id="intellisense-status" class="${this._config.enableIntelliSense ? 'enabled' : 'disabled'}">
            智能提示: ${this._config.enableIntelliSense ? '已启用' : '已禁用'}
          </div>
        </div>
      </footer>
    </div>
    `;
    
    // 注入Monaco配置，让前端脚本可以访问
    const configScript = `
      window.editorConfig = ${JSON.stringify(this._config)};
      
      // 添加特定语言的配置
      window.languageSpecificOptions = {
        javascript: ${JSON.stringify(monacoService.getLanguageSpecificOptions('javascript'))},
        typescript: ${JSON.stringify(monacoService.getLanguageSpecificOptions('typescript'))},
        html: ${JSON.stringify(monacoService.getLanguageSpecificOptions('html'))},
        css: ${JSON.stringify(monacoService.getLanguageSpecificOptions('css'))},
        json: ${JSON.stringify(monacoService.getLanguageSpecificOptions('json'))}
      };
    `;
    
    // 创建包含Monaco加载脚本和编辑器初始化的完整HTML
    const html = this.generateHtml(
      'Monaco编辑器',
      bodyContent,
      ['dist/webviewScripts/monaco-panel.js'],
      ['media/styles/monaco-panel.css']
    );
    
    // 在</head>前插入Monaco加载脚本和配置脚本
    return html.replace(
      '</head>',
      `<script>${monacoLoaderScript}</script><script>${configScript}</script></head>`
    );
  }
  
  /**
   * 处理从WebView接收到的消息
   */
  protected _onDidReceiveMessage(message: any): void {
    switch (message.command) {
      case 'applyChanges':
        this.applyChanges(message.content);
        break;
      case 'refreshContent':
        this.loadFromActiveEditor();
        break;
      case 'editorReady':
        // 编辑器已准备好，可以更新内容
        this.postMessage({ command: 'updateContent', content: this._config.value });
        break;
      case 'contentChanged':
        // 内容已更改，但尚未应用
        break;
      case 'languageChanged':
        // 语言已更改
        if (message.language) {
          const monacoService = MonacoService.getInstance();
          monacoService.configureEditor({ 
            language: message.language,
            options: monacoService.getLanguageSpecificOptions(message.language)
          });
        }
        break;
      case 'cursorPositionChanged':
        // 光标位置已更改，可以记录或更新状态
        break;
      case 'toggleIntelliSense':
        // 切换智能提示状态
        const monacoService = MonacoService.getInstance();
        monacoService.configureEditor({ 
          enableIntelliSense: message.enabled
        });
        break;
      case 'alert':
        vscode.window.showInformationMessage(message.text);
        break;
    }
  }
  
  /**
   * 应用编辑器更改到活动文档
   */
  private async applyChanges(content: string): Promise<void> {
    const monacoService = MonacoService.getInstance();
    const result = await monacoService.applyToActiveEditor(content);
    
    if (result) {
      vscode.window.showInformationMessage('已应用编辑器更改');
    } else {
      vscode.window.showErrorMessage('应用编辑器更改失败');
    }
  }
} 