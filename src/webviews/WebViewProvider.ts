import * as vscode from 'vscode';
import * as path from 'path';

/**
 * WebView提供者基类，为所有WebView面板提供通用功能
 */
export abstract class WebViewProvider implements vscode.Disposable {
  protected readonly _panel: vscode.WebviewPanel;
  protected _disposables: vscode.Disposable[] = [];
  protected readonly _extensionUri: vscode.Uri;

  /**
   * 创建WebView提供者
   * @param extensionUri 扩展URI
   * @param viewType WebView类型
   * @param title 面板标题
   * @param viewColumn 视图列位置
   * @param options WebView选项
   */
  constructor(
    extensionUri: vscode.Uri,
    viewType: string,
    title: string,
    viewColumn: vscode.ViewColumn = vscode.ViewColumn.One,
    options: vscode.WebviewPanelOptions & vscode.WebviewOptions = {}
  ) {
    this._extensionUri = extensionUri;

    // 创建WebView面板
    this._panel = vscode.window.createWebviewPanel(
      viewType,
      title,
      viewColumn,
      {
        // 启用JavaScript
        enableScripts: true,
        // 限制可以加载的资源
        localResourceRoots: [
          vscode.Uri.joinPath(extensionUri, 'media'),
          vscode.Uri.joinPath(extensionUri, 'dist')
        ],
        // 保持WebView在后台
        retainContextWhenHidden: true,
        ...options
      }
    );

    // 设置HTML内容
    this._panel.webview.html = this._getHtmlForWebview();

    // 处理WebView关闭事件
    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

    // 处理WebView发来的消息
    this._panel.webview.onDidReceiveMessage(
      this._onDidReceiveMessage.bind(this),
      null,
      this._disposables
    );
  }

  /**
   * 获取WebView的HTML内容
   */
  protected abstract _getHtmlForWebview(): string;

  /**
   * 处理从WebView接收到的消息
   * @param message WebView发送的消息
   */
  protected abstract _onDidReceiveMessage(message: any): void;

  /**
   * 向WebView发送消息
   * @param message 消息对象
   */
  public async postMessage(message: any): Promise<boolean> {
    return this._panel.webview.postMessage(message);
  }

  /**
   * 获取本地资源的WebView URI
   * @param relativePath 相对路径
   */
  protected getAssetUri(relativePath: string): vscode.Uri {
    const diskPath = vscode.Uri.joinPath(this._extensionUri, relativePath);
    return this._panel.webview.asWebviewUri(diskPath);
  }

  /**
   * 获取随机的nonce值用于内容安全策略
   */
  protected getNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }

  /**
   * 生成基本的HTML模板
   * @param title 页面标题
   * @param bodyContent 页面正文内容
   * @param scripts 要加载的脚本
   * @param styles 要加载的样式
   */
  protected generateHtml(
    title: string,
    bodyContent: string,
    scripts: string[] = [],
    styles: string[] = []
  ): string {
    const nonce = this.getNonce();

    // 构建脚本标签
    const scriptTags = scripts
      .map(script => {
        const scriptUri = this.getAssetUri(script);
        return `<script nonce="${nonce}" src="${scriptUri}"></script>`;
      })
      .join('\n');

    // 构建样式标签
    const styleTags = styles
      .map(style => {
        const styleUri = this.getAssetUri(style);
        return `<link rel="stylesheet" nonce="${nonce}" href="${styleUri}">`;
      })
      .join('\n');

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <meta http-equiv="Content-Security-Policy" content="default-src 'none'; script-src 'nonce-${nonce}'; style-src 'nonce-${nonce}'; img-src ${this._panel.webview.cspSource} https:; font-src ${this._panel.webview.cspSource};">
  ${styleTags}
</head>
<body>
  ${bodyContent}
  ${scriptTags}
</body>
</html>`;
  }

  /**
   * 生成包含内联CSS的HTML模板
   * @param title 页面标题
   * @param bodyContent 页面正文内容
   * @param inlineStyle 内联CSS样式
   * @param scripts 要加载的脚本
   */
  protected generateHtmlWithInlineStyle(
    title: string,
    bodyContent: string,
    inlineStyle: string,
    scripts: string[] = []
  ): string {
    const nonce = this.getNonce();

    // 构建脚本标签
    const scriptTags = scripts
      .map(script => {
        const scriptUri = this.getAssetUri(script);
        return `<script nonce="${nonce}" src="${scriptUri}"></script>`;
      })
      .join('\n');

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <meta http-equiv="Content-Security-Policy" content="default-src 'none'; script-src 'nonce-${nonce}'; style-src 'unsafe-inline' ${this._panel.webview.cspSource}; img-src ${this._panel.webview.cspSource} https:; font-src ${this._panel.webview.cspSource};">
  <style>
    ${inlineStyle}
  </style>
</head>
<body>
  ${bodyContent}
  ${scriptTags}
</body>
</html>`;
  }

  /**
   * 显示面板
   */
  public reveal(): void {
    this._panel.reveal();
  }

  /**
   * 处理资源释放
   */
  public dispose(): void {
    this._panel.dispose();

    // 清理所有注册的可释放资源
    while (this._disposables.length) {
      const disposable = this._disposables.pop();
      if (disposable) {
        disposable.dispose();
      }
    }
  }
} 