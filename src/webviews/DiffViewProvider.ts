import * as vscode from 'vscode';
import { DiffService } from '../services/DiffService';

/**
 * 差异视图项目类
 * 用于在树视图中显示不同类型的差异比较选项
 */
export class DiffTreeItem extends vscode.TreeItem {
  /**
   * 构造函数
   * @param label 标签
   * @param collapsibleState 可折叠状态
   * @param command 命令
   */
  constructor(
    public readonly label: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState,
    command?: vscode.Command
  ) {
    super(label, collapsibleState);
    
    if (command) {
      this.command = command;
    }
    
    // 设置图标
    this.iconPath = new vscode.ThemeIcon('diff');
  }
  
  /**
   * 上下文值，用于右键菜单
   */
  contextValue = 'diffItem';
}

/**
 * 差异比较视图提供者类
 * 用于在侧边栏显示差异比较选项
 */
export class DiffViewProvider implements vscode.TreeDataProvider<DiffTreeItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<DiffTreeItem | undefined | null | void> = new vscode.EventEmitter<DiffTreeItem | undefined | null | void>();
  readonly onDidChangeTreeData: vscode.Event<DiffTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

  /**
   * 构造函数
   * @param diffService 差异比较服务
   */
  constructor(private diffService: DiffService) {}

  /**
   * 刷新视图
   */
  refresh(): void {
    this._onDidChangeTreeData.fire();
  }

  /**
   * 获取树项
   * @param element 树元素
   */
  getTreeItem(element: DiffTreeItem): vscode.TreeItem {
    return element;
  }

  /**
   * 获取子元素
   * @param element 父元素
   */
  getChildren(element?: DiffTreeItem): Thenable<DiffTreeItem[]> {
    // 根级别
    if (!element) {
      const items: DiffTreeItem[] = [];
      
      // 添加差异比较面板选项
      items.push(
        new DiffTreeItem('差异比较面板', vscode.TreeItemCollapsibleState.None, {
          command: 'lazycode.showDiffPanel',
          title: '显示差异比较面板'
        })
      );
      
      // 添加比较分类
      items.push(new DiffTreeItem('文件比较', vscode.TreeItemCollapsibleState.Expanded));
      items.push(new DiffTreeItem('代码比较', vscode.TreeItemCollapsibleState.Expanded));
      
      return Promise.resolve(items);
    } 
    // 文件比较分类
    else if (element.label === '文件比较') {
      return Promise.resolve([
        new DiffTreeItem('比较两个文件', vscode.TreeItemCollapsibleState.None, {
          command: 'lazycode.compareFiles',
          title: '比较两个文件'
        })
      ]);
    }
    // 代码比较分类
    else if (element.label === '代码比较') {
      return Promise.resolve([
        new DiffTreeItem('与剪贴板比较', vscode.TreeItemCollapsibleState.None, {
          command: 'lazycode.compareWithClipboard',
          title: '与剪贴板比较'
        }),
        new DiffTreeItem('与上一版本比较', vscode.TreeItemCollapsibleState.None, {
          command: 'lazycode.compareWithPrevious',
          title: '与上一版本比较'
        })
      ]);
    }
    
    return Promise.resolve([]);
  }
} 