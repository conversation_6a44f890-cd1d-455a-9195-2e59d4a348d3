import * as vscode from 'vscode';
import { WebViewProvider } from './WebViewProvider';
import { EditSuggestion, NextEditService } from '../services/NextEditService';

/**
 * 编辑建议面板类
 */
export class NextEditPanel extends WebViewProvider {
  private static instance: NextEditPanel | undefined;
  private static readonly viewType = 'lazycode.nextEditPanel';
  private _suggestions: EditSuggestion[] = [];

  /**
   * 获取单例实例
   */
  public static getInstance(extensionUri: vscode.Uri): NextEditPanel {
    if (!NextEditPanel.instance) {
      NextEditPanel.instance = new NextEditPanel(extensionUri);
    }
    return NextEditPanel.instance;
  }

  /**
   * 创建或显示面板
   */
  public static render(extensionUri: vscode.Uri): NextEditPanel {
    const panel = NextEditPanel.getInstance(extensionUri);
    panel.reveal();
    
    // 显示面板后加载建议
    panel.loadSuggestions();
    
    return panel;
  }

  /**
   * 私有构造函数，用于创建编辑建议面板
   */
  private constructor(extensionUri: vscode.Uri) {
    super(extensionUri, NextEditPanel.viewType, '编辑建议', vscode.ViewColumn.Two);
    
    // 监听建议变化事件
    const nextEditService = NextEditService.getInstance();
    nextEditService.onSuggestionsChanged(suggestions => {
      this._suggestions = suggestions;
      this.updateContent();
    });
  }

  /**
   * 加载编辑建议
   */
  private async loadSuggestions(): Promise<void> {
    const nextEditService = NextEditService.getInstance();
    this._suggestions = await nextEditService.getSuggestions();
    this.updateContent();
  }

  /**
   * 更新面板内容
   */
  private updateContent(): void {
    if (this._panel && this._panel.webview) {
      this._panel.webview.html = this._getHtmlForWebview();
    }
  }

  /**
   * 获取WebView的HTML内容
   */
  protected _getHtmlForWebview(): string {
    // 生成建议项HTML
    let suggestionsHtml = '';
    
    if (this._suggestions.length === 0) {
      suggestionsHtml = `
        <div class="empty-state">
          <p>目前没有可用的编辑建议。</p>
          <button id="refreshSuggestions">刷新建议</button>
        </div>
      `;
    } else {
      // 为每个建议创建HTML
      this._suggestions.forEach(suggestion => {
        suggestionsHtml += `
          <div class="suggestion-item" data-id="${suggestion.id}">
            <h3>${suggestion.title}</h3>
            ${suggestion.description ? `<p class="description">${suggestion.description}</p>` : ''}
            <pre class="code-snippet language-${suggestion.language}">${this.escapeHtml(suggestion.code)}</pre>
            <div class="actions">
              <button class="apply-btn" data-id="${suggestion.id}">应用此建议</button>
              <button class="reject-btn" data-id="${suggestion.id}">拒绝</button>
            </div>
          </div>
        `;
      });
    }

    // 为编辑建议面板创建HTML内容
    const bodyContent = `
    <div class="container">
      <header>
        <h1>编辑建议</h1>
        <div class="header-actions">
          <button id="refreshSuggestions" title="刷新建议">刷新</button>
        </div>
      </header>
      <main>
        <section class="suggestions">
          ${suggestionsHtml}
        </section>
      </main>
    </div>
    `;

    return this.generateHtml(
      '编辑建议',
      bodyContent,
      ['dist/webviewScripts/next-edit-panel.js'],
      ['media/styles/next-edit-panel.css']
    );
  }

  /**
   * HTML转义，防止XSS
   */
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  /**
   * 处理从WebView接收到的消息
   */
  protected _onDidReceiveMessage(message: any): void {
    switch (message.command) {
      case 'applySuggestion':
        this.applySuggestion(message.suggestionId);
        break;
      case 'rejectSuggestion':
        vscode.window.showInformationMessage('编辑建议已拒绝');
        break;
      case 'refreshSuggestions':
        this.loadSuggestions();
        break;
      case 'alert':
        vscode.window.showInformationMessage(message.text);
        break;
    }
  }

  /**
   * 应用编辑建议
   */
  private async applySuggestion(suggestionId: string): Promise<void> {
    const nextEditService = NextEditService.getInstance();
    const result = await nextEditService.applySuggestion(suggestionId);
    
    if (result) {
      vscode.window.showInformationMessage('编辑建议已应用');
    } else {
      vscode.window.showErrorMessage('应用编辑建议失败');
    }
  }
} 