import * as vscode from 'vscode';
import { WebViewProvider } from './WebViewProvider';
import { MemoryService, MemoryItem, MemoryItemType } from '../services/MemoryService';
import { MemorySearchOptions } from '../utils/MemorySearch';

/**
 * 记忆面板类
 */
export class MemoryPanel extends WebViewProvider {
  private static instance: MemoryPanel | undefined;
  private static readonly viewType = 'lazycode.memoryPanel';
  private _memories: MemoryItem[] = [];

  /**
   * 获取单例实例
   */
  public static getInstance(extensionUri: vscode.Uri): MemoryPanel {
    if (!MemoryPanel.instance) {
      MemoryPanel.instance = new MemoryPanel(extensionUri);
    }
    return MemoryPanel.instance;
  }

  /**
   * 创建或显示面板
   */
  public static render(extensionUri: vscode.Uri): MemoryPanel {
    const panel = MemoryPanel.getInstance(extensionUri);
    panel.reveal();
    
    // 显示面板后加载记忆
    panel.loadMemories();
    
    return panel;
  }

  /**
   * 私有构造函数，用于创建记忆面板
   */
  private constructor(extensionUri: vscode.Uri) {
    super(extensionUri, MemoryPanel.viewType, '代码记忆', vscode.ViewColumn.One);
    
    // 监听记忆变化事件
    const memoryService = MemoryService.getInstance();
    memoryService.onMemoriesChanged(memories => {
      this._memories = memories;
      this.updateContent();
    });
  }

  /**
   * 加载记忆
   */
  private async loadMemories(): Promise<void> {
    const memoryService = MemoryService.getInstance();
    this._memories = await memoryService.loadMemories();
    this.updateContent();
  }

  /**
   * 更新面板内容
   */
  private updateContent(): void {
    if (this._panel && this._panel.webview) {
      this._panel.webview.html = this._getHtmlForWebview();
    }
  }

  /**
   * 获取WebView的HTML内容
   */
  protected _getHtmlForWebview(): string {
    // 按类型分组记忆
    const memoriesByType = this.groupMemoriesByType();
    let memoriesHtml = '';
    
    if (this._memories.length === 0) {
      memoriesHtml = `
        <div class="empty-state">
          <p>目前没有保存的记忆。</p>
          <button id="captureMemory">捕获新记忆</button>
        </div>
      `;
    } else {
      // 创建快速访问区域（星标记忆）
      const starredMemories = this._memories.filter(memory => memory.isStarred);
      if (starredMemories.length > 0) {
        memoriesHtml += `
          <div class="memory-category">
            <h2>⭐ 星标记忆 (${starredMemories.length})</h2>
            <div class="memory-list">
        `;
        
        starredMemories.forEach(memory => {
          memoriesHtml += this.createMemoryItemHtml(memory);
        });
        
        memoriesHtml += `
            </div>
          </div>
        `;
      }
      
      // 为每个记忆类型创建部分
      for (const [type, memories] of Object.entries(memoriesByType)) {
        if (memories.length === 0) {
          continue;
        }
        
        const typeTitle = this.getMemoryTypeDisplayName(type as MemoryItemType);
        
        memoriesHtml += `
          <div class="memory-category">
            <h2>${typeTitle} (${memories.length})</h2>
            <div class="memory-list">
        `;
        
        // 为每个记忆创建HTML
        memories.forEach(memory => {
          memoriesHtml += this.createMemoryItemHtml(memory);
        });
        
        memoriesHtml += `
            </div>
          </div>
        `;
      }
    }

    // 生成常用标签HTML
    const memoryService = MemoryService.getInstance();
    const commonTags = memoryService.getCommonTags();
    let commonTagsHtml = '';
    
    if (commonTags.length > 0) {
      commonTagsHtml = `
        <div class="common-tags">
          <h3>常用标签</h3>
          <div class="tag-cloud">
      `;
      
      commonTags.forEach(tag => {
        // 根据使用次数调整标签大小
        const fontSize = Math.max(0.8, Math.min(1.4, 0.8 + tag.count * 0.1));
        commonTagsHtml += `
          <span class="tag-item" data-tag="${tag.tag}" style="font-size: ${fontSize}em">
            ${tag.tag} <small>(${tag.count})</small>
          </span>
        `;
      });
      
      commonTagsHtml += `
          </div>
        </div>
      `;
    }
    
    // 生成语言过滤器HTML
    const usedLanguages = memoryService.getUsedLanguages();
    let languagesFilterHtml = '';
    
    if (usedLanguages.length > 0) {
      languagesFilterHtml = `
        <div class="language-filter">
          <label>语言过滤:</label>
          <select id="languageFilter">
            <option value="">全部语言</option>
      `;
      
      usedLanguages.forEach(lang => {
        languagesFilterHtml += `<option value="${lang.language}">${lang.language} (${lang.count})</option>`;
      });
      
      languagesFilterHtml += `
          </select>
        </div>
      `;
    }

    // 为记忆面板创建HTML内容
    const bodyContent = `
    <div class="container">
      <header>
        <h1>代码记忆</h1>
        <div class="header-actions">
          <input type="text" id="searchInput" placeholder="搜索记忆..." />
          <button id="advancedSearchBtn" title="高级搜索">高级</button>
          <button id="captureMemory" title="捕获新记忆">捕获</button>
          <button id="refreshMemories" title="刷新记忆">刷新</button>
          <button id="importMemory" title="导入记忆">导入</button>
        </div>
      </header>
      <main>
        <div class="sidebar">
          ${commonTagsHtml}
          <div class="filter-section">
            <h3>记忆过滤</h3>
            <div class="memory-filter">
              <button class="filter-btn active" data-filter="all">全部</button>
              <button class="filter-btn" data-filter="code_snippet">代码片段</button>
              <button class="filter-btn" data-filter="file_template">文件模板</button>
              <button class="filter-btn" data-filter="edit_operation">编辑操作</button>
              <button class="filter-btn" data-filter="custom">自定义</button>
              <button class="filter-btn" data-filter="starred">星标</button>
            </div>
            ${languagesFilterHtml}
            <div class="sort-options">
              <label>排序方式:</label>
              <select id="sortBySelect">
                <option value="lastUsed">最近使用</option>
                <option value="usage">使用频率</option>
                <option value="name">名称</option>
                <option value="created">创建时间</option>
              </select>
              <select id="sortOrderSelect">
                <option value="desc">降序</option>
                <option value="asc">升序</option>
              </select>
            </div>
          </div>
        </div>
        <div class="content">
          <section class="memories">
            ${memoriesHtml}
          </section>
        </div>
      </main>
      
      <div id="memoryEditor" class="modal">
        <div class="modal-content">
          <div class="modal-header">
            <h2 id="editorTitle">捕获记忆</h2>
            <span class="close-btn">&times;</span>
          </div>
          <div class="modal-body">
            <form id="memoryForm">
              <input type="hidden" id="memoryId" value="">
              <div class="form-group">
                <label for="memoryName">记忆名称</label>
                <input type="text" id="memoryName" required>
              </div>
              <div class="form-group">
                <label for="memoryDescription">记忆描述</label>
                <input type="text" id="memoryDescription" required>
              </div>
              <div class="form-group">
                <label for="memoryType">记忆类型</label>
                <select id="memoryType" required>
                  <option value="code_snippet">代码片段</option>
                  <option value="file_template">文件模板</option>
                  <option value="edit_operation">编辑操作</option>
                  <option value="custom">自定义</option>
                </select>
              </div>
              <div class="form-group">
                <label for="memoryLanguages">适用语言 (逗号分隔)</label>
                <input type="text" id="memoryLanguages" placeholder="javascript,typescript,html">
              </div>
              <div class="form-group">
                <label for="memoryTags">标签 (逗号分隔)</label>
                <input type="text" id="memoryTags" placeholder="函数,工具,样式">
              </div>
              <div class="form-group content-group">
                <label for="memoryContent">记忆内容</label>
                <textarea id="memoryContent" rows="10" required></textarea>
              </div>
              <div class="form-group">
                <label for="memoryStarred">
                  <input type="checkbox" id="memoryStarred">
                  星标记忆
                </label>
              </div>
              <div class="form-actions">
                <button type="submit" id="saveMemory">保存</button>
                <button type="button" id="cancelMemory">取消</button>
              </div>
            </form>
          </div>
        </div>
      </div>
      
      <div id="advancedSearchModal" class="modal">
        <div class="modal-content">
          <div class="modal-header">
            <h2>高级搜索</h2>
            <span class="close-btn">&times;</span>
          </div>
          <div class="modal-body">
            <form id="advancedSearchForm">
              <div class="form-group">
                <label for="searchQuery">关键词</label>
                <input type="text" id="searchQuery" placeholder="输入搜索关键词">
              </div>
              <div class="form-group">
                <label for="searchType">记忆类型</label>
                <select id="searchType">
                  <option value="all">全部类型</option>
                  <option value="code_snippet">代码片段</option>
                  <option value="file_template">文件模板</option>
                  <option value="edit_operation">编辑操作</option>
                  <option value="custom">自定义</option>
                </select>
              </div>
              <div class="form-group">
                <label for="searchLanguage">编程语言</label>
                <select id="searchLanguage">
                  <option value="">全部语言</option>
                  ${usedLanguages.map(lang => 
                    `<option value="${lang.language}">${lang.language}</option>`
                  ).join('')}
                </select>
              </div>
              <div class="form-group">
                <label for="searchTags">标签 (逗号分隔)</label>
                <input type="text" id="searchTags" placeholder="输入标签">
              </div>
              <div class="form-group">
                <label for="searchStarred">
                  <input type="checkbox" id="searchStarred">
                  只显示星标记忆
                </label>
              </div>
              <div class="form-group">
                <label for="searchSortBy">排序方式</label>
                <select id="searchSortBy">
                  <option value="relevance">相关性</option>
                  <option value="lastUsed">最近使用</option>
                  <option value="usage">使用频率</option>
                  <option value="name">名称</option>
                  <option value="created">创建时间</option>
                </select>
              </div>
              <div class="form-group">
                <label for="searchSortOrder">排序顺序</label>
                <select id="searchSortOrder">
                  <option value="desc">降序</option>
                  <option value="asc">升序</option>
                </select>
              </div>
              <div class="form-actions">
                <button type="submit" id="doAdvancedSearch">搜索</button>
                <button type="button" id="resetSearch">重置</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    `;

    return this.generateHtml(
      '代码记忆',
      bodyContent,
      ['dist/webviewScripts/memory-panel.js'],
      ['media/styles/memory-panel.css']
    );
  }

  /**
   * 创建记忆项HTML
   */
  private createMemoryItemHtml(memory: MemoryItem): string {
    const starsClass = memory.isStarred ? 'starred' : '';
    const lastUsedDate = new Date(memory.lastUsedAt).toLocaleDateString();
    const truncatedContent = memory.content.length > 100 ? 
      memory.content.substring(0, 100) + '...' : 
      memory.content;
    
    return `
      <div class="memory-item ${starsClass}" data-id="${memory.id}" data-type="${memory.type}">
        <div class="memory-header">
          <h3>${memory.name}</h3>
          <div class="memory-controls">
            <button class="star-btn ${starsClass}" data-id="${memory.id}" title="${memory.isStarred ? '取消星标' : '添加星标'}">
              ${memory.isStarred ? '★' : '☆'}
            </button>
            <button class="apply-btn" data-id="${memory.id}" title="应用记忆">应用</button>
            <button class="edit-btn" data-id="${memory.id}" title="编辑记忆">编辑</button>
            <button class="delete-btn" data-id="${memory.id}" title="删除记忆">删除</button>
          </div>
        </div>
        <p class="description">${memory.description}</p>
        <pre class="content">${this.escapeHtml(truncatedContent)}</pre>
        <div class="memory-meta">
          <span class="tag">类型: ${this.getMemoryTypeDisplayName(memory.type)}</span>
          ${memory.languages.length > 0 ? `<span class="tag">语言: ${memory.languages.join(', ')}</span>` : ''}
          ${memory.tags.length > 0 ? `<span class="tag">标签: ${memory.tags.join(', ')}</span>` : ''}
          <span class="usage">使用次数: ${memory.usageCount}</span>
          <span class="last-used">最后使用: ${lastUsedDate}</span>
        </div>
      </div>
    `;
  }

  /**
   * 转义HTML特殊字符
   */
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }

  /**
   * 获取记忆类型显示名称
   */
  private getMemoryTypeDisplayName(type: MemoryItemType): string {
    switch (type) {
      case MemoryItemType.CODE_SNIPPET:
        return '代码片段';
      case MemoryItemType.EDIT_OPERATION:
        return '编辑操作';
      case MemoryItemType.FILE_TEMPLATE:
        return '文件模板';
      case MemoryItemType.CUSTOM:
        return '自定义';
      default:
        return '未知';
    }
  }

  /**
   * 按类型分组记忆
   */
  private groupMemoriesByType(): Record<string, MemoryItem[]> {
    const groups: Record<string, MemoryItem[]> = {
      [MemoryItemType.CODE_SNIPPET]: [],
      [MemoryItemType.EDIT_OPERATION]: [],
      [MemoryItemType.FILE_TEMPLATE]: [],
      [MemoryItemType.CUSTOM]: []
    };
    
    this._memories.forEach(memory => {
      if (groups[memory.type]) {
        groups[memory.type].push(memory);
      } else {
        groups[MemoryItemType.CUSTOM].push(memory);
      }
    });
    
    return groups;
  }

  /**
   * 处理从WebView接收到的消息
   */
  protected _onDidReceiveMessage(message: any): void {
    const memoryService = MemoryService.getInstance();
    
    switch (message.command) {
      case 'captureMemory':
        // 处理捕获记忆的请求
        this.captureMemory(message.name, message.description, message.type, message.tags);
        break;
      case 'createMemory':
        // 处理创建记忆的请求
        this.createMemory(message.memory);
        break;
      case 'updateMemory':
        // 处理更新记忆的请求
        this.updateMemory(message.memoryId, message.memory);
        break;
      case 'deleteMemory':
        // 处理删除记忆的请求
        this.deleteMemory(message.memoryId);
        break;
      case 'applyMemory':
        // 处理应用记忆的请求
        this.applyMemory(message.memoryId);
        break;
      case 'toggleStarred':
        // 处理切换星标状态的请求
        this.toggleStarred(message.memoryId);
        break;
      case 'getMemoryDetails':
        // 处理获取记忆详情的请求
        this.getMemoryDetails(message.memoryId);
        break;
      case 'searchMemories':
        // 处理搜索记忆的请求
        this.searchMemories(message.query);
        break;
      case 'filterMemories':
        // 处理过滤记忆的请求
        this.filterMemories(message.filterType);
        break;
      case 'importMemory':
        // 处理导入记忆的请求
        this.importMemory();
        break;
      case 'exportMemory':
        // 处理导出记忆的请求
        this.exportMemory(message.memoryId);
        break;
      case 'refreshMemories':
        // 处理刷新记忆的请求
        this.loadMemories();
        break;
      case 'alert':
        // 显示消息
        vscode.window.showInformationMessage(message.text);
        break;
      case 'advancedSearch':
        // 处理高级搜索请求
        this.advancedSearch(message.options);
        break;
    }
  }

  /**
   * 捕获记忆
   */
  private async captureMemory(
    name: string, 
    description: string, 
    type: MemoryItemType = MemoryItemType.CODE_SNIPPET,
    tags: string[] = []
  ): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      
      const memory = await memoryService.captureFromEditor(name, description, type, tags);
      
      if (memory) {
        vscode.window.showInformationMessage(`记忆 "${name}" 已捕获`);
      } else {
        vscode.window.showErrorMessage('捕获记忆失败，请确保编辑器中有内容或已选择内容');
      }
    } catch (error) {
      console.error('捕获记忆失败:', error);
      vscode.window.showErrorMessage('捕获记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 创建记忆
   */
  private async createMemory(memory: any): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      
      // 将字符串语言和标签列表转换为数组
      const languages = memory.languages ? memory.languages.split(',').map((s: string) => s.trim()).filter(Boolean) : [];
      const tags = memory.tags ? memory.tags.split(',').map((s: string) => s.trim()).filter(Boolean) : [];
      
      const newMemory = await memoryService.createMemory({
        name: memory.name,
        description: memory.description,
        type: memory.type as MemoryItemType,
        content: memory.content,
        languages,
        tags,
        isStarred: memory.isStarred
      });
      
      vscode.window.showInformationMessage(`记忆 "${memory.name}" 已创建`);
    } catch (error) {
      console.error('创建记忆失败:', error);
      vscode.window.showErrorMessage('创建记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 更新记忆
   */
  private async updateMemory(memoryId: string, memory: any): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      
      // 将字符串语言和标签列表转换为数组
      const languages = memory.languages ? memory.languages.split(',').map((s: string) => s.trim()).filter(Boolean) : [];
      const tags = memory.tags ? memory.tags.split(',').map((s: string) => s.trim()).filter(Boolean) : [];
      
      const updatedMemory = await memoryService.updateMemory(memoryId, {
        name: memory.name,
        description: memory.description,
        type: memory.type as MemoryItemType,
        content: memory.content,
        languages,
        tags,
        isStarred: memory.isStarred
      });
      
      if (updatedMemory) {
        vscode.window.showInformationMessage(`记忆 "${memory.name}" 已更新`);
      } else {
        vscode.window.showErrorMessage(`未找到ID为 "${memoryId}" 的记忆`);
      }
    } catch (error) {
      console.error('更新记忆失败:', error);
      vscode.window.showErrorMessage('更新记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 删除记忆
   */
  private async deleteMemory(memoryId: string): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      const memory = memoryService.getMemoryById(memoryId);
      
      if (!memory) {
        vscode.window.showErrorMessage(`未找到ID为 "${memoryId}" 的记忆`);
        return;
      }
      
      const result = await memoryService.deleteMemory(memoryId);
      
      if (result) {
        vscode.window.showInformationMessage(`记忆 "${memory.name}" 已删除`);
      } else {
        vscode.window.showErrorMessage(`删除记忆 "${memory.name}" 失败`);
      }
    } catch (error) {
      console.error('删除记忆失败:', error);
      vscode.window.showErrorMessage('删除记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 应用记忆
   */
  private async applyMemory(memoryId: string): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      const memory = memoryService.getMemoryById(memoryId);
      
      if (!memory) {
        vscode.window.showErrorMessage(`未找到ID为 "${memoryId}" 的记忆`);
        return;
      }
      
      const result = await memoryService.applyToEditor(memoryId);
      
      if (result) {
        vscode.window.showInformationMessage(`已应用记忆 "${memory.name}"`);
      } else {
        vscode.window.showErrorMessage(`应用记忆 "${memory.name}" 失败`);
      }
    } catch (error) {
      console.error('应用记忆失败:', error);
      vscode.window.showErrorMessage('应用记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 切换记忆星标状态
   */
  private async toggleStarred(memoryId: string): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      const result = await memoryService.toggleStarred(memoryId);
      
      if (!result) {
        vscode.window.showErrorMessage(`未找到ID为 "${memoryId}" 的记忆`);
      }
    } catch (error) {
      console.error('切换星标状态失败:', error);
      vscode.window.showErrorMessage('切换星标状态失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 获取记忆详情
   */
  private async getMemoryDetails(memoryId: string): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      const memory = memoryService.getMemoryById(memoryId);
      
      if (memory) {
        // 发送记忆详情到WebView
        this.postMessage({
          command: 'memoryDetails',
          memory: {
            ...memory,
            languages: memory.languages.join(', '),
            tags: memory.tags.join(', ')
          }
        });
      } else {
        vscode.window.showErrorMessage(`未找到ID为 "${memoryId}" 的记忆`);
      }
    } catch (error) {
      console.error('获取记忆详情失败:', error);
      vscode.window.showErrorMessage('获取记忆详情失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 搜索记忆
   */
  private async searchMemories(query: string): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      const memories = memoryService.searchMemories(query);
      
      // 发送搜索结果到WebView
      this.postMessage({
        command: 'searchResults',
        memories
      });
    } catch (error) {
      console.error('搜索记忆失败:', error);
      vscode.window.showErrorMessage('搜索记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 过滤记忆
   */
  private async filterMemories(filterType: string): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      let memories: MemoryItem[] = [];
      
      if (filterType === 'all') {
        memories = memoryService.getAllMemories();
      } else if (filterType === 'starred') {
        memories = memoryService.getStarredMemories();
      } else {
        memories = memoryService.getMemoriesByType(filterType as MemoryItemType);
      }
      
      // 发送过滤结果到WebView
      this.postMessage({
        command: 'filterResults',
        memories
      });
    } catch (error) {
      console.error('过滤记忆失败:', error);
      vscode.window.showErrorMessage('过滤记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 导入记忆
   */
  private async importMemory(): Promise<void> {
    try {
      // 显示文件选择对话框
      const result = await vscode.window.showOpenDialog({
        canSelectFiles: true,
        canSelectFolders: false,
        canSelectMany: false,
        filters: {
          'JSON文件': ['json']
        },
        title: '选择记忆文件'
      });
      
      if (result && result.length > 0) {
        const memoryService = MemoryService.getInstance();
        const filePath = result[0].fsPath;
        
        const memory = await memoryService.importMemory(filePath);
        
        if (memory) {
          vscode.window.showInformationMessage(`记忆 "${memory.name}" 已导入`);
        } else {
          vscode.window.showErrorMessage('导入记忆失败');
        }
      }
    } catch (error) {
      console.error('导入记忆失败:', error);
      vscode.window.showErrorMessage('导入记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 导出记忆
   */
  private async exportMemory(memoryId: string): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      const memory = memoryService.getMemoryById(memoryId);
      
      if (!memory) {
        vscode.window.showErrorMessage(`未找到ID为 "${memoryId}" 的记忆`);
        return;
      }
      
      // 显示保存文件对话框
      const result = await vscode.window.showSaveDialog({
        defaultUri: vscode.Uri.file(`${memory.name}.json`),
        filters: {
          'JSON文件': ['json']
        },
        title: '保存记忆'
      });
      
      if (result) {
        const filePath = result.fsPath;
        
        const success = await memoryService.exportMemory(memoryId, filePath);
        
        if (success) {
          vscode.window.showInformationMessage(`记忆 "${memory.name}" 已导出到 ${filePath}`);
        } else {
          vscode.window.showErrorMessage(`导出记忆 "${memory.name}" 失败`);
        }
      }
    } catch (error) {
      console.error('导出记忆失败:', error);
      vscode.window.showErrorMessage('导出记忆失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 高级搜索
   */
  private async advancedSearch(options: MemorySearchOptions): Promise<void> {
    try {
      const memoryService = MemoryService.getInstance();
      let memories: MemoryItem[] = [];
      
      // 使用相关性搜索或高级搜索
      if (options.sortBy === 'relevance' && options.query) {
        memories = memoryService.searchByRelevance(options.query);
      } else {
        memories = memoryService.advancedSearch(options);
      }
      
      // 发送搜索结果到WebView
      this.postMessage({
        command: 'searchResults',
        memories,
        searchOptions: options
      });
    } catch (error) {
      console.error('高级搜索失败:', error);
      vscode.window.showErrorMessage('高级搜索失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
} 