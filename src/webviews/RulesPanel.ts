import * as vscode from 'vscode';
import { WebViewProvider } from './WebViewProvider';
import { Rule, RuleType, RulesService } from '../services/RulesService';

/**
 * 规则面板类
 */
export class RulesPanel extends WebViewProvider {
  private static instance: RulesPanel | undefined;
  private static readonly viewType = 'lazycode.rulesPanel';
  private _rules: Rule[] = [];

  /**
   * 获取单例实例
   */
  public static getInstance(extensionUri: vscode.Uri): RulesPanel {
    if (!RulesPanel.instance) {
      RulesPanel.instance = new RulesPanel(extensionUri);
    }
    return RulesPanel.instance;
  }

  /**
   * 创建或显示面板
   */
  public static render(extensionUri: vscode.Uri): RulesPanel {
    const panel = RulesPanel.getInstance(extensionUri);
    panel.reveal();
    
    // 显示面板后加载规则
    panel.loadRules();
    
    return panel;
  }

  /**
   * 私有构造函数，用于创建规则面板
   */
  private constructor(extensionUri: vscode.Uri) {
    super(extensionUri, RulesPanel.viewType, '代码规则', vscode.ViewColumn.One);
    
    // 监听规则变化事件
    const rulesService = RulesService.getInstance();
    rulesService.onRulesChanged(rules => {
      this._rules = rules;
      this.updateContent();
    });
  }

  /**
   * 加载规则
   */
  private async loadRules(): Promise<void> {
    const rulesService = RulesService.getInstance();
    this._rules = await rulesService.loadRules();
    this.updateContent();
  }

  /**
   * 更新面板内容
   */
  private updateContent(): void {
    if (this._panel && this._panel.webview) {
      this._panel.webview.html = this._getHtmlForWebview();
    }
  }

  /**
   * 获取WebView的HTML内容
   */
  protected _getHtmlForWebview(): string {
    // 生成规则分类HTML
    const rulesByType = this.groupRulesByType();
    let rulesHtml = '';
    
    if (this._rules.length === 0) {
      rulesHtml = `
        <div class="empty-state">
          <p>目前没有可用的规则。</p>
          <button id="createRule">创建新规则</button>
        </div>
      `;
    } else {
      // 为每个规则类型创建部分
      for (const [type, rules] of Object.entries(rulesByType)) {
        const typeTitle = this.getRuleTypeDisplayName(type as RuleType);
        
        rulesHtml += `
          <div class="rule-category">
            <h2>${typeTitle} (${rules.length})</h2>
            <div class="rule-list">
        `;
        
        // 为每个规则创建HTML
        rules.forEach(rule => {
          rulesHtml += `
            <div class="rule-item" data-id="${rule.id}">
              <div class="rule-header">
                <h3>${rule.name}</h3>
                <div class="rule-controls">
                  <label class="switch">
                    <input type="checkbox" class="rule-toggle" data-id="${rule.id}" ${rule.enabled ? 'checked' : ''}>
                    <span class="slider"></span>
                  </label>
                  <button class="apply-btn" data-id="${rule.id}" title="应用规则">应用</button>
                  <button class="edit-btn" data-id="${rule.id}" title="编辑规则">编辑</button>
                  <button class="delete-btn" data-id="${rule.id}" title="删除规则">删除</button>
                </div>
              </div>
              <p class="description">${rule.description}</p>
              <div class="rule-meta">
                <span class="tag">类型: ${this.getRuleTypeDisplayName(rule.type)}</span>
                ${rule.languages.length > 0 ? `<span class="tag">语言: ${rule.languages.join(', ')}</span>` : ''}
                ${rule.tags.length > 0 ? `<span class="tag">标签: ${rule.tags.join(', ')}</span>` : ''}
              </div>
            </div>
          `;
        });
        
        rulesHtml += `
            </div>
          </div>
        `;
      }
    }

    // 为规则面板创建HTML内容
    const bodyContent = `
    <div class="container">
      <header>
        <h1>代码规则</h1>
        <div class="header-actions">
          <button id="createRule" title="创建新规则">创建规则</button>
          <button id="refreshRules" title="刷新规则">刷新</button>
        </div>
      </header>
      <main>
        <section class="rules">
          ${rulesHtml}
        </section>
      </main>
      <div id="ruleEditor" class="modal">
        <div class="modal-content">
          <div class="modal-header">
            <h2 id="editorTitle">创建规则</h2>
            <span class="close-btn">&times;</span>
          </div>
          <div class="modal-body">
            <form id="ruleForm">
              <input type="hidden" id="ruleId" value="">
              <div class="form-group">
                <label for="ruleName">规则名称</label>
                <input type="text" id="ruleName" required>
              </div>
              <div class="form-group">
                <label for="ruleDescription">规则描述</label>
                <input type="text" id="ruleDescription" required>
              </div>
              <div class="form-group">
                <label for="ruleType">规则类型</label>
                <select id="ruleType" required>
                  <option value="generation">代码生成</option>
                  <option value="analysis">代码分析</option>
                  <option value="transformation">代码转换</option>
                  <option value="custom">自定义</option>
                </select>
              </div>
              <div class="form-group">
                <label for="ruleLanguages">适用语言 (逗号分隔)</label>
                <input type="text" id="ruleLanguages" placeholder="javascript,typescript,html">
              </div>
              <div class="form-group">
                <label for="ruleTags">标签 (逗号分隔)</label>
                <input type="text" id="ruleTags" placeholder="格式化,注释,风格">
              </div>
              <div class="form-group">
                <label for="ruleContent">规则内容</label>
                <textarea id="ruleContent" rows="10" required></textarea>
              </div>
              <div class="form-group">
                <label for="ruleEnabled">
                  <input type="checkbox" id="ruleEnabled" checked>
                  启用规则
                </label>
              </div>
              <div class="form-actions">
                <button type="submit" id="saveRule">保存</button>
                <button type="button" id="cancelRule">取消</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    `;

    return this.generateHtml(
      '代码规则',
      bodyContent,
      ['dist/webviewScripts/rules-panel.js'],
      ['media/styles/rules-panel.css']
    );
  }

  /**
   * 获取规则类型显示名称
   */
  private getRuleTypeDisplayName(type: RuleType): string {
    switch (type) {
      case RuleType.GENERATION:
        return '代码生成';
      case RuleType.ANALYSIS:
        return '代码分析';
      case RuleType.TRANSFORMATION:
        return '代码转换';
      case RuleType.CUSTOM:
        return '自定义';
      default:
        return '未知';
    }
  }

  /**
   * 按类型分组规则
   */
  private groupRulesByType(): Record<string, Rule[]> {
    const groups: Record<string, Rule[]> = {
      [RuleType.GENERATION]: [],
      [RuleType.ANALYSIS]: [],
      [RuleType.TRANSFORMATION]: [],
      [RuleType.CUSTOM]: []
    };
    
    this._rules.forEach(rule => {
      if (groups[rule.type]) {
        groups[rule.type].push(rule);
      } else {
        groups[RuleType.CUSTOM].push(rule);
      }
    });
    
    return groups;
  }

  /**
   * 处理从WebView接收到的消息
   */
  protected _onDidReceiveMessage(message: any): void {
    const rulesService = RulesService.getInstance();
    
    switch (message.command) {
      case 'createRule':
        // 处理创建规则的请求
        this.createRule(message.rule);
        break;
      case 'updateRule':
        // 处理更新规则的请求
        this.updateRule(message.ruleId, message.rule);
        break;
      case 'deleteRule':
        // 处理删除规则的请求
        this.deleteRule(message.ruleId);
        break;
      case 'toggleRule':
        // 处理切换规则启用状态的请求
        this.toggleRuleEnabled(message.ruleId, message.enabled);
        break;
      case 'getRuleDetails':
        // 处理获取规则详情的请求
        this.getRuleDetails(message.ruleId);
        break;
      case 'refreshRules':
        // 处理刷新规则的请求
        this.loadRules();
        break;
      case 'alert':
        // 显示警告消息
        vscode.window.showInformationMessage(message.text);
        break;
      case 'applyRule':
        // 应用规则
        vscode.commands.executeCommand('lazycode.applyRule', message.ruleId);
        break;
    }
  }

  /**
   * 创建规则
   */
  private async createRule(rule: any): Promise<void> {
    try {
      const rulesService = RulesService.getInstance();
      
      // 将字符串语言和标签列表转换为数组
      const languages = rule.languages ? rule.languages.split(',').map((s: string) => s.trim()).filter(Boolean) : [];
      const tags = rule.tags ? rule.tags.split(',').map((s: string) => s.trim()).filter(Boolean) : [];
      
      const newRule = await rulesService.createRule({
        name: rule.name,
        description: rule.description,
        type: rule.type as RuleType,
        content: rule.content,
        enabled: rule.enabled,
        order: 0,
        languages,
        tags
      });
      
      vscode.window.showInformationMessage(`规则 "${rule.name}" 已创建`);
    } catch (error) {
      console.error('创建规则失败:', error);
      vscode.window.showErrorMessage('创建规则失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 更新规则
   */
  private async updateRule(ruleId: string, rule: any): Promise<void> {
    try {
      const rulesService = RulesService.getInstance();
      
      // 将字符串语言和标签列表转换为数组
      const languages = rule.languages ? rule.languages.split(',').map((s: string) => s.trim()).filter(Boolean) : [];
      const tags = rule.tags ? rule.tags.split(',').map((s: string) => s.trim()).filter(Boolean) : [];
      
      const updatedRule = await rulesService.updateRule(ruleId, {
        name: rule.name,
        description: rule.description,
        type: rule.type as RuleType,
        content: rule.content,
        enabled: rule.enabled,
        languages,
        tags
      });
      
      if (updatedRule) {
        vscode.window.showInformationMessage(`规则 "${rule.name}" 已更新`);
      } else {
        vscode.window.showErrorMessage(`未找到ID为 "${ruleId}" 的规则`);
      }
    } catch (error) {
      console.error('更新规则失败:', error);
      vscode.window.showErrorMessage('更新规则失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 删除规则
   */
  private async deleteRule(ruleId: string): Promise<void> {
    try {
      const rulesService = RulesService.getInstance();
      const rule = rulesService.getRuleById(ruleId);
      
      if (!rule) {
        vscode.window.showErrorMessage(`未找到ID为 "${ruleId}" 的规则`);
        return;
      }
      
      const result = await rulesService.deleteRule(ruleId);
      
      if (result) {
        vscode.window.showInformationMessage(`规则 "${rule.name}" 已删除`);
      } else {
        vscode.window.showErrorMessage(`删除规则 "${rule.name}" 失败`);
      }
    } catch (error) {
      console.error('删除规则失败:', error);
      vscode.window.showErrorMessage('删除规则失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 切换规则启用状态
   */
  private async toggleRuleEnabled(ruleId: string, enabled: boolean): Promise<void> {
    try {
      const rulesService = RulesService.getInstance();
      const rule = rulesService.getRuleById(ruleId);
      
      if (!rule) {
        vscode.window.showErrorMessage(`未找到ID为 "${ruleId}" 的规则`);
        return;
      }
      
      const updatedRule = await rulesService.updateRule(ruleId, { enabled });
      
      if (updatedRule) {
        const status = enabled ? '启用' : '禁用';
        vscode.window.showInformationMessage(`规则 "${rule.name}" 已${status}`);
      }
    } catch (error) {
      console.error('切换规则状态失败:', error);
      vscode.window.showErrorMessage('切换规则状态失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 获取规则详情
   */
  private async getRuleDetails(ruleId: string): Promise<void> {
    try {
      const rulesService = RulesService.getInstance();
      const rule = rulesService.getRuleById(ruleId);
      
      if (rule) {
        // 发送规则详情到WebView
        this.postMessage({
          command: 'ruleDetails',
          rule: {
            ...rule,
            languages: rule.languages.join(', '),
            tags: rule.tags.join(', ')
          }
        });
      } else {
        vscode.window.showErrorMessage(`未找到ID为 "${ruleId}" 的规则`);
      }
    } catch (error) {
      console.error('获取规则详情失败:', error);
      vscode.window.showErrorMessage('获取规则详情失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
} 