import * as vscode from 'vscode';
import { WebViewProvider } from './WebViewProvider';

/**
 * 主面板类
 */
export class MainPanel extends WebViewProvider {
  private static instance: MainPanel | undefined;
  private static readonly viewType = 'lazycode.mainPanel';

  /**
   * 获取单例实例
   */
  public static getInstance(extensionUri: vscode.Uri): MainPanel {
    if (!MainPanel.instance) {
      MainPanel.instance = new MainPanel(extensionUri);
    }
    return MainPanel.instance;
  }

  /**
   * 创建或显示面板
   */
  public static render(extensionUri: vscode.Uri): MainPanel {
    const panel = MainPanel.getInstance(extensionUri);
    panel.reveal();
    return panel;
  }

  /**
   * 私有构造函数，用于创建主面板
   */
  private constructor(extensionUri: vscode.Uri) {
    super(extensionUri, MainPanel.viewType, 'LazyCode', vscode.ViewColumn.One);
  }

  /**
   * 获取WebView的HTML内容
   */
  protected _getHtmlForWebview(): string {
    // 为主面板创建一个简单的HTML内容
    const bodyContent = `
    <div class="container">
      <header>
        <h1>LazyCode 主面板</h1>
      </header>
      <main>
        <section class="welcome">
          <h2>欢迎使用 LazyCode</h2>
          <p>LazyCode 是一个强大的AI辅助编程工具。</p>
          <button id="nextEditBtn">显示下一步编辑建议</button>
        </section>
      </main>
    </div>
    `;

    return this.generateHtml(
      'LazyCode', 
      bodyContent,
      ['dist/webviewScripts/main-panel.js'],
      ['media/styles/main-panel.css']
    );
  }

  /**
   * 处理从WebView接收到的消息
   */
  protected _onDidReceiveMessage(message: any): void {
    switch (message.command) {
      case 'showNextEdit':
        // 处理显示下一步编辑建议的请求
        vscode.commands.executeCommand('lazycode.showNextEdit');
        break;
      case 'alert':
        // 显示警告消息
        vscode.window.showInformationMessage(message.text);
        break;
    }
  }
} 