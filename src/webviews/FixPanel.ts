import * as vscode from 'vscode';
import { WebViewProvider } from './WebViewProvider';
import { DiagnosticService, DiagnosticIssue, DiagnosticSeverity, DiagnosticSource } from '../services/DiagnosticService';

/**
 * 自动修复面板类
 */
export class FixPanel extends WebViewProvider {
  private static instance: FixPanel | undefined;
  private static readonly viewType = 'lazycode.fixPanel';
  private _issues: DiagnosticIssue[] = [];
  private _currentFilter: string = 'all';

  /**
   * 获取单例实例
   */
  public static getInstance(extensionUri: vscode.Uri): FixPanel {
    if (!FixPanel.instance) {
      FixPanel.instance = new FixPanel(extensionUri);
    }
    return FixPanel.instance;
  }

  /**
   * 创建或显示面板
   */
  public static render(extensionUri: vscode.Uri): FixPanel {
    const panel = FixPanel.getInstance(extensionUri);
    panel.reveal();
    
    // 显示面板后加载当前诊断问题
    panel.loadIssues();
    
    return panel;
  }

  /**
   * 私有构造函数，用于创建自动修复面板
   */
  private constructor(extensionUri: vscode.Uri) {
    super(extensionUri, FixPanel.viewType, '代码自动修复', vscode.ViewColumn.One);
    
    // 监听诊断变化事件
    const diagnosticService = DiagnosticService.getInstance();
    
    diagnosticService.onDiagnosticsChanged(issues => {
      this._issues = issues;
      this.updateContent();
    });
  }

  /**
   * 加载诊断问题
   */
  private async loadIssues(): Promise<void> {
    const diagnosticService = DiagnosticService.getInstance();
    
    // 如果当前有活动编辑器，则获取其诊断问题
    if (vscode.window.activeTextEditor) {
      const document = vscode.window.activeTextEditor.document;
      await diagnosticService.diagnoseDocument(document);
    }
    
    this._issues = diagnosticService.getIssues();
    this.updateContent();
  }

  /**
   * 更新面板内容
   */
  private updateContent(): void {
    if (this._panel && this._panel.webview) {
      this._panel.webview.html = this._getHtmlForWebview();
    }
  }

  /**
   * 获取WebView的HTML内容
   */
  protected _getHtmlForWebview(): string {
    // 过滤诊断问题
    let filteredIssues = this._issues;
    
    if (this._currentFilter !== 'all') {
      filteredIssues = this._issues.filter(issue => {
        if (this._currentFilter === 'error') {
          return issue.severity === DiagnosticSeverity.ERROR;
        } else if (this._currentFilter === 'warning') {
          return issue.severity === DiagnosticSeverity.WARNING;
        } else if (this._currentFilter === 'hint') {
          return issue.severity === DiagnosticSeverity.HINT;
        } else if (this._currentFilter === 'info') {
          return issue.severity === DiagnosticSeverity.INFO;
        } else if (this._currentFilter === 'rule') {
          return issue.source === DiagnosticSource.RULE;
        } else if (this._currentFilter === 'syntax') {
          return issue.source === DiagnosticSource.SYNTAX;
        } else if (this._currentFilter === 'style') {
          return issue.source === DiagnosticSource.STYLE;
        }
        return false;
      });
    }
    
    // 按严重性和文件分组问题
    const issuesByFile: { [key: string]: DiagnosticIssue[] } = {};
    
    filteredIssues.forEach(issue => {
      const fileUri = issue.file.toString();
      if (!issuesByFile[fileUri]) {
        issuesByFile[fileUri] = [];
      }
      issuesByFile[fileUri].push(issue);
    });
    
    // 生成问题HTML
    let issuesHtml = '';
    
    if (Object.keys(issuesByFile).length === 0) {
      issuesHtml = `
        <div class="empty-state">
          <p>当前没有检测到代码问题。</p>
          <p>打开一个文件并编辑内容，系统将自动检测问题并提供修复建议。</p>
          <button id="refreshIssues">刷新问题</button>
        </div>
      `;
    } else {
      // 显示问题计数
      const errorCount = filteredIssues.filter(issue => issue.severity === DiagnosticSeverity.ERROR).length;
      const warningCount = filteredIssues.filter(issue => issue.severity === DiagnosticSeverity.WARNING).length;
      const hintCount = filteredIssues.filter(issue => issue.severity === DiagnosticSeverity.HINT).length;
      const infoCount = filteredIssues.filter(issue => issue.severity === DiagnosticSeverity.INFO).length;
      
      issuesHtml = `
        <div class="issues-summary">
          <div class="summary-item error">
            <span class="count">${errorCount}</span>
            <span class="label">错误</span>
          </div>
          <div class="summary-item warning">
            <span class="count">${warningCount}</span>
            <span class="label">警告</span>
          </div>
          <div class="summary-item hint">
            <span class="count">${hintCount}</span>
            <span class="label">提示</span>
          </div>
          <div class="summary-item info">
            <span class="count">${infoCount}</span>
            <span class="label">信息</span>
          </div>
          <div class="summary-actions">
            <button id="fixAllIssues" title="修复所有问题">一键修复</button>
            <button id="fixSelectedIssues" title="修复选中区域内的问题">修复选中区域</button>
          </div>
        </div>
      `;
      
      // 为每个文件创建问题列表
      for (const [fileUri, issues] of Object.entries(issuesByFile)) {
        const fileName = this.getFileNameFromUri(fileUri);
        
        issuesHtml += `
          <div class="file-issues">
            <div class="file-header">
              <h3>${fileName}</h3>
              <span class="issue-count">问题: ${issues.length}</span>
            </div>
            <div class="issues-list">
        `;
        
        // 排序问题：错误 > 警告 > 提示 > 信息
        const sortedIssues = [...issues].sort((a, b) => {
          const severityOrder = {
            [DiagnosticSeverity.ERROR]: 0,
            [DiagnosticSeverity.WARNING]: 1,
            [DiagnosticSeverity.HINT]: 2,
            [DiagnosticSeverity.INFO]: 3
          };
          
          return severityOrder[a.severity] - severityOrder[b.severity];
        });
        
        // 为每个问题创建HTML
        sortedIssues.forEach(issue => {
          issuesHtml += this.createIssueItemHtml(issue);
        });
        
        issuesHtml += `
            </div>
          </div>
        `;
      }
    }

    // 为自动修复面板创建HTML内容
    const bodyContent = `
    <div class="container">
      <header>
        <h1>代码自动修复</h1>
        <div class="header-actions">
          <button id="refreshIssues" title="刷新问题">刷新</button>
          <button id="clearIssues" title="清除所有诊断">清除</button>
        </div>
      </header>
      
      <main>
        <div class="sidebar">
          <div class="filter-section">
            <h3>问题过滤</h3>
            <div class="filter-buttons">
              <button class="filter-btn ${this._currentFilter === 'all' ? 'active' : ''}" data-filter="all">全部</button>
              <button class="filter-btn ${this._currentFilter === 'error' ? 'active' : ''}" data-filter="error">错误</button>
              <button class="filter-btn ${this._currentFilter === 'warning' ? 'active' : ''}" data-filter="warning">警告</button>
              <button class="filter-btn ${this._currentFilter === 'hint' ? 'active' : ''}" data-filter="hint">提示</button>
              <button class="filter-btn ${this._currentFilter === 'info' ? 'active' : ''}" data-filter="info">信息</button>
            </div>
            
            <h3>问题来源</h3>
            <div class="filter-buttons">
              <button class="filter-btn ${this._currentFilter === 'rule' ? 'active' : ''}" data-filter="rule">规则</button>
              <button class="filter-btn ${this._currentFilter === 'syntax' ? 'active' : ''}" data-filter="syntax">语法</button>
              <button class="filter-btn ${this._currentFilter === 'style' ? 'active' : ''}" data-filter="style">样式</button>
            </div>
          </div>
        </div>
        
        <div class="content">
          <section class="issues">
            ${issuesHtml}
          </section>
        </div>
      </main>
      
      <div id="fixPreviewModal" class="modal">
        <div class="modal-content">
          <div class="modal-header">
            <h2>修复预览</h2>
            <span class="close-btn">&times;</span>
          </div>
          <div class="modal-body">
            <div class="preview-container">
              <div class="preview-header">
                <h3>修复前</h3>
              </div>
              <pre id="beforePreview" class="preview-content"></pre>
              
              <div class="preview-header">
                <h3>修复后</h3>
              </div>
              <pre id="afterPreview" class="preview-content"></pre>
            </div>
            <div class="preview-actions">
              <button id="applyFix">应用修复</button>
              <button id="cancelFix">取消</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    `;

    return this.generateHtml(
      '代码自动修复',
      bodyContent,
      ['dist/webviewScripts/fix-panel.js'],
      ['media/styles/fix-panel.css']
    );
  }

  /**
   * 从URI获取文件名
   * @param uri 文件URI
   */
  private getFileNameFromUri(uri: string): string {
    // 提取文件名
    const match = uri.match(/([^/\\]+)$/);
    if (match) {
      return decodeURIComponent(match[1]);
    }
    return uri;
  }

  /**
   * 创建问题项HTML
   * @param issue 诊断问题
   */
  private createIssueItemHtml(issue: DiagnosticIssue): string {
    const severityClass = this.getSeverityClass(issue.severity);
    const sourceDisplay = this.getSourceDisplay(issue.source);
    const location = `行 ${issue.range.start.line + 1}, 列 ${issue.range.start.character + 1}`;
    
    return `
      <div class="issue-item ${severityClass}" data-id="${issue.id}">
        <div class="issue-header">
          <div class="issue-severity ${severityClass}">
            ${this.getSeverityIcon(issue.severity)}
          </div>
          <div class="issue-title">
            <h4>${this.escapeHtml(issue.message)}</h4>
            <div class="issue-meta">
              <span class="issue-source">${sourceDisplay}</span>
              <span class="issue-location">${location}</span>
              ${issue.ruleId ? `<span class="issue-rule">规则: ${issue.ruleId}</span>` : ''}
            </div>
          </div>
        </div>
        
        <div class="issue-content">
          ${issue.context ? `<pre class="issue-context">${this.escapeHtml(issue.context)}</pre>` : ''}
        </div>
        
        <div class="issue-actions">
          ${issue.fixes && issue.fixes.length > 0 ? 
            `<button class="fix-btn" data-id="${issue.id}">查看修复</button>` : 
            '<span class="no-fix">无可用修复</span>'}
          <button class="goto-btn" data-id="${issue.id}">转到位置</button>
        </div>
      </div>
    `;
  }

  /**
   * 获取严重性类名
   * @param severity 严重性
   */
  private getSeverityClass(severity: DiagnosticSeverity): string {
    switch (severity) {
      case DiagnosticSeverity.ERROR:
        return 'error';
      case DiagnosticSeverity.WARNING:
        return 'warning';
      case DiagnosticSeverity.HINT:
        return 'hint';
      case DiagnosticSeverity.INFO:
        return 'info';
      default:
        return '';
    }
  }

  /**
   * 获取严重性图标
   * @param severity 严重性
   */
  private getSeverityIcon(severity: DiagnosticSeverity): string {
    switch (severity) {
      case DiagnosticSeverity.ERROR:
        return '❌';
      case DiagnosticSeverity.WARNING:
        return '⚠️';
      case DiagnosticSeverity.HINT:
        return '💡';
      case DiagnosticSeverity.INFO:
        return 'ℹ️';
      default:
        return '';
    }
  }

  /**
   * 获取来源显示名称
   * @param source 来源
   */
  private getSourceDisplay(source: DiagnosticSource): string {
    switch (source) {
      case DiagnosticSource.RULE:
        return '规则检查';
      case DiagnosticSource.SYNTAX:
        return '语法分析';
      case DiagnosticSource.STYLE:
        return '样式检查';
      case DiagnosticSource.PERFORMANCE:
        return '性能检查';
      case DiagnosticSource.SECURITY:
        return '安全检查';
      case DiagnosticSource.OTHER:
        return '其他来源';
      default:
        return '未知来源';
    }
  }

  /**
   * 转义HTML特殊字符
   * @param text 文本
   */
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }

  /**
   * 处理从WebView接收到的消息
   */
  protected _onDidReceiveMessage(message: any): void {
    const diagnosticService = DiagnosticService.getInstance();
    
    switch (message.command) {
      case 'refreshIssues':
        // 刷新诊断问题
        this.loadIssues();
        break;
        
      case 'clearIssues':
        // 清除所有诊断
        diagnosticService.clearDiagnostics();
        this._issues = [];
        this.updateContent();
        break;
        
      case 'filterIssues':
        // 过滤问题
        this._currentFilter = message.filter;
        this.updateContent();
        break;
        
      case 'getFixDetails':
        // 获取修复详情
        this.getFixDetails(message.issueId);
        break;
        
      case 'applyFix':
        // 应用修复
        this.applyFix(message.issueId, message.fixIndex);
        break;
        
      case 'gotoIssue':
        // 跳转到问题位置
        this.gotoIssueLocation(message.issueId);
        break;
        
      case 'fixAllIssues':
        // 修复所有问题
        this.fixAllIssues();
        break;
        
      case 'fixSelectedIssues':
        // 修复选中区域内的问题
        this.fixSelectedIssues();
        break;
    }
  }

  /**
   * 获取修复详情
   * @param issueId 问题ID
   */
  private getFixDetails(issueId: string): void {
    // 查找问题
    const issue = this._issues.find(issue => issue.id === issueId);
    
    if (issue && issue.fixes && issue.fixes.length > 0) {
      // 获取第一个修复建议
      const fix = issue.fixes[0];
      
      // 发送修复详情到WebView
      this.postMessage({
        command: 'fixDetails',
        issueId,
        fixIndex: 0,
        title: fix.title,
        description: fix.description,
        beforePreview: fix.beforePreview || issue.context || '',
        afterPreview: fix.afterPreview || ''
      });
    }
  }

  /**
   * 应用修复
   * @param issueId 问题ID
   * @param fixIndex 修复索引
   */
  private async applyFix(issueId: string, fixIndex: number): Promise<void> {
    try {
      const diagnosticService = DiagnosticService.getInstance();
      
      // 查找问题
      const issue = this._issues.find(issue => issue.id === issueId);
      
      if (!issue) {
        vscode.window.showErrorMessage('未找到指定的问题');
        return;
      }
      
      // 应用修复
      const result = await diagnosticService.applyFix(issue, fixIndex);
      
      if (result) {
        vscode.window.showInformationMessage('成功应用修复');
        
        // 重新诊断文档
        if (vscode.window.activeTextEditor) {
          await diagnosticService.diagnoseDocument(vscode.window.activeTextEditor.document);
        }
      } else {
        vscode.window.showErrorMessage('应用修复失败');
      }
    } catch (error) {
      console.error('应用修复失败:', error);
      vscode.window.showErrorMessage('应用修复失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 跳转到问题位置
   * @param issueId 问题ID
   */
  private async gotoIssueLocation(issueId: string): Promise<void> {
    try {
      // 查找问题
      const issue = this._issues.find(issue => issue.id === issueId);
      
      if (!issue) {
        vscode.window.showErrorMessage('未找到指定的问题');
        return;
      }
      
      // 打开文件
      const document = await vscode.workspace.openTextDocument(issue.file);
      const editor = await vscode.window.showTextDocument(document);
      
      // 跳转到问题位置
      editor.selection = new vscode.Selection(issue.range.start, issue.range.end);
      
      // 滚动到可见区域
      editor.revealRange(issue.range, vscode.TextEditorRevealType.InCenter);
    } catch (error) {
      console.error('跳转到问题位置失败:', error);
      vscode.window.showErrorMessage('跳转到问题位置失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 修复所有问题
   */
  private async fixAllIssues(): Promise<void> {
    try {
      const diagnosticService = DiagnosticService.getInstance();
      
      // 获取当前过滤器状态，用于决定要修复哪些类型的问题
      const options: {
        severities?: DiagnosticSeverity[],
        sources?: DiagnosticSource[],
        fixOnlySelected?: boolean
      } = {};
      
      // 如果有设置过滤器，则只修复过滤后的问题类型
      if (this._currentFilter !== 'all') {
        if (this._currentFilter === 'error') {
          options.severities = [DiagnosticSeverity.ERROR];
        } else if (this._currentFilter === 'warning') {
          options.severities = [DiagnosticSeverity.WARNING];
        } else if (this._currentFilter === 'hint') {
          options.severities = [DiagnosticSeverity.HINT];
        } else if (this._currentFilter === 'info') {
          options.severities = [DiagnosticSeverity.INFO];
        } else if (this._currentFilter === 'rule') {
          options.sources = [DiagnosticSource.RULE];
        } else if (this._currentFilter === 'syntax') {
          options.sources = [DiagnosticSource.SYNTAX];
        } else if (this._currentFilter === 'style') {
          options.sources = [DiagnosticSource.STYLE];
        }
      }
      
      // 如果当前有活动编辑器，则修复其中的问题
      if (vscode.window.activeTextEditor) {
        const uri = vscode.window.activeTextEditor.document.uri;
        const count = await diagnosticService.fixAllIssues(uri, options);
        
        if (count > 0) {
          vscode.window.showInformationMessage(`成功修复 ${count} 个问题`);
          
          // 重新诊断文档
          await diagnosticService.diagnoseDocument(vscode.window.activeTextEditor.document);
        } else {
          vscode.window.showInformationMessage('没有可修复的问题');
        }
      } else {
        // 修复所有问题
        const count = await diagnosticService.fixAllIssues(undefined, options);
        
        if (count > 0) {
          vscode.window.showInformationMessage(`成功修复 ${count} 个问题`);
        } else {
          vscode.window.showInformationMessage('没有可修复的问题');
        }
      }
    } catch (error) {
      console.error('修复所有问题失败:', error);
      vscode.window.showErrorMessage('修复所有问题失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 修复选中区域内的问题
   */
  private async fixSelectedIssues(): Promise<void> {
    try {
      // 检查是否有活动编辑器和选中区域
      if (!vscode.window.activeTextEditor || vscode.window.activeTextEditor.selection.isEmpty) {
        vscode.window.showInformationMessage('请先在编辑器中选择一段代码');
        return;
      }
      
      const diagnosticService = DiagnosticService.getInstance();
      const uri = vscode.window.activeTextEditor.document.uri;
      
      // 获取当前过滤器状态
      const options: {
        severities?: DiagnosticSeverity[],
        sources?: DiagnosticSource[],
        fixOnlySelected: boolean
      } = {
        fixOnlySelected: true
      };
      
      // 应用当前过滤器设置
      if (this._currentFilter !== 'all') {
        if (this._currentFilter === 'error') {
          options.severities = [DiagnosticSeverity.ERROR];
        } else if (this._currentFilter === 'warning') {
          options.severities = [DiagnosticSeverity.WARNING];
        } else if (this._currentFilter === 'hint') {
          options.severities = [DiagnosticSeverity.HINT];
        } else if (this._currentFilter === 'info') {
          options.severities = [DiagnosticSeverity.INFO];
        } else if (this._currentFilter === 'rule') {
          options.sources = [DiagnosticSource.RULE];
        } else if (this._currentFilter === 'syntax') {
          options.sources = [DiagnosticSource.SYNTAX];
        } else if (this._currentFilter === 'style') {
          options.sources = [DiagnosticSource.STYLE];
        }
      }
      
      // 修复选中区域内的问题
      const count = await diagnosticService.fixAllIssues(uri, options);
      
      if (count > 0) {
        vscode.window.showInformationMessage(`成功修复选中区域内的 ${count} 个问题`);
        
        // 重新诊断文档
        await diagnosticService.diagnoseDocument(vscode.window.activeTextEditor.document);
      } else {
        vscode.window.showInformationMessage('选中区域内没有可修复的问题');
      }
    } catch (error) {
      console.error('修复选中区域内的问题失败:', error);
      vscode.window.showErrorMessage('修复选中区域内的问题失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
} 