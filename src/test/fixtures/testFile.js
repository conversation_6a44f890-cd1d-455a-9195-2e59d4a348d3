// 这是一个用于测试诊断功能的JS文件

// 1. 测试未闭合的括号
function testUnclosedBrackets(param1) {
  // 修复了缺少的闭合括号
}

// 2. 测试拼写错误
function testFunction() {
  // 拼写错误: functoin 已改为 function
  return true; // 拼写错误: retrun 已改为 return
}

// 3. 测试冗余分号
let x = 5;;

// 4. 测试冗余逗号
const obj = {
  a: 1,
  b: 2,
};

// 5. 测试过长的行
const veryLongLine = "这是一个非常长的行，超过了120个字符限制，应该在适当的位置被拆分。这样的长行会降低代码的可读性，应该避免出现在代码中。通常，我们建议将每行代码控制在120个字符以内。";

// 6. 测试命名规范
const user_name = "<PERSON>"; // 应该使用驼峰命名法: userName

// 7. 测试字符串引号未闭合
const unClosedString = "这是一个未闭合的字符串";

// 8. 测试上下文感知的修复建议
const array = [1, 2, 3]; // 修复了缺少的闭合括号 