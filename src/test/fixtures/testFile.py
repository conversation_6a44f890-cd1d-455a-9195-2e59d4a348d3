# 这是一个用于测试Python相关诊断功能的文件

# 1. 测试缩进问题
def test_indentation():
    correct_indent = "这行缩进正确"
   incorrect_indent = "这行缩进不正确，不是4的倍数"
      also_incorrect = "这行缩进也不正确"

# 2. 测试未闭合的括号
def test_unclosed_brackets(param1, param2:
    # 缺少闭合括号
    value = (1 + 2 * (3 - 4
    return value

# 3. 测试PEP 8行长度
very_long_line = "这是一个超过了PEP 8建议的79个字符限制的长行，应该在适当的位置被拆分。根据PEP 8规范，Python代码的每行应该不超过79个字符。"

# 4. 测试多重嵌套的括号
def test_nested_brackets():
    result = func1(func2(func3(1, 2), func4(3, 4)), func5(func6(5, 6), 7))
    return result 