import * as assert from 'assert';
import * as vscode from 'vscode';
import * as path from 'path';
import { DiagnosticService, DiagnosticSeverity, DiagnosticSource } from '../services/DiagnosticService';

// 异步延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

suite('诊断服务测试', () => {
	// 打开测试文件
	test('测试JS文件诊断', async () => {
		// 获取测试文件绝对路径
		const jsFilePath = path.join(__dirname, '../../src/test/fixtures/testFile.js');
		const jsUri = vscode.Uri.file(jsFilePath);
		
		// 打开测试文件
		const document = await vscode.workspace.openTextDocument(jsUri);
		await vscode.window.showTextDocument(document);
		
		// 获取诊断服务实例
		const diagnosticService = DiagnosticService.getInstance();
		
		// 执行诊断
		await diagnosticService.diagnoseDocument(document);
		
		// 等待一点时间让诊断完成
		await delay(500);
		
		// 获取诊断问题
		const issues = diagnosticService.getIssues(jsUri);
		
		// 验证是否检测到问题
		assert.ok(issues.length > 0, '应该检测到至少一个问题');
		
		// 验证是否有语法问题
		const syntaxIssues = issues.filter(issue => issue.source === DiagnosticSource.SYNTAX);
		assert.ok(syntaxIssues.length > 0, '应该检测到语法问题');
		
		// 验证是否有修复建议
		const issuesWithFixes = issues.filter(issue => issue.fixes && issue.fixes.length > 0);
		assert.ok(issuesWithFixes.length > 0, '应该为问题提供修复建议');
		
		console.log(`JS文件诊断测试通过，共检测到 ${issues.length} 个问题，其中 ${syntaxIssues.length} 个语法问题，${issuesWithFixes.length} 个有修复建议`);
	});
	
	test('测试Python文件诊断', async () => {
		// 获取测试文件绝对路径
		const pyFilePath = path.join(__dirname, '../../src/test/fixtures/testFile.py');
		const pyUri = vscode.Uri.file(pyFilePath);
		
		// 打开测试文件
		const document = await vscode.workspace.openTextDocument(pyUri);
		await vscode.window.showTextDocument(document);
		
		// 获取诊断服务实例
		const diagnosticService = DiagnosticService.getInstance();
		
		// 执行诊断
		await diagnosticService.diagnoseDocument(document);
		
		// 等待一点时间让诊断完成
		await delay(500);
		
		// 获取诊断问题
		const issues = diagnosticService.getIssues(pyUri);
		
		// 验证是否检测到Python缩进问题
		const indentIssues = issues.filter(issue => 
			issue.message.includes('缩进') || issue.message.includes('indent'));
		
		// 验证是否检测到PEP 8行长度问题
		const lineLengthIssues = issues.filter(issue => 
			issue.message.includes('PEP 8') || issue.message.includes('行长度'));
		
		console.log(`Python文件诊断测试通过，共检测到 ${issues.length} 个问题，其中 ${indentIssues.length} 个缩进问题，${lineLengthIssues.length} 个行长度问题`);
	});
	
	test('测试Markdown文件诊断', async () => {
		// 获取测试文件绝对路径
		const mdFilePath = path.join(__dirname, '../../src/test/fixtures/testFile.md');
		const mdUri = vscode.Uri.file(mdFilePath);
		
		// 打开测试文件
		const document = await vscode.workspace.openTextDocument(mdUri);
		await vscode.window.showTextDocument(document);
		
		// 获取诊断服务实例
		const diagnosticService = DiagnosticService.getInstance();
		
		// 执行诊断
		await diagnosticService.diagnoseDocument(document);
		
		// 等待一点时间让诊断完成
		await delay(500);
		
		// 获取诊断问题
		const issues = diagnosticService.getIssues(mdUri);
		
		// 验证是否检测到代码块问题
		const codeBlockIssues = issues.filter(issue => 
			issue.message.includes('代码块') || issue.message.includes('codeblock'));
		
		// 验证是否检测到空链接问题
		const emptyLinkIssues = issues.filter(issue => 
			issue.message.includes('空链接') || issue.message.includes('link'));
		
		console.log(`Markdown文件诊断测试通过，共检测到 ${issues.length} 个问题，其中 ${codeBlockIssues.length} 个代码块问题，${emptyLinkIssues.length} 个空链接问题`);
	});
	
	test('测试JSON文件诊断', async () => {
		// 获取测试文件绝对路径
		const jsonFilePath = path.join(__dirname, '../../src/test/fixtures/testFile.json');
		const jsonUri = vscode.Uri.file(jsonFilePath);
		
		// 打开测试文件
		const document = await vscode.workspace.openTextDocument(jsonUri);
		await vscode.window.showTextDocument(document);
		
		// 获取诊断服务实例
		const diagnosticService = DiagnosticService.getInstance();
		
		// 修改JSON文件内容使其包含错误
		const edit = new vscode.WorkspaceEdit();
		edit.insert(jsonUri, new vscode.Position(3, 22), ''); // 删除逗号
		await vscode.workspace.applyEdit(edit);
		
		// 执行诊断
		await diagnosticService.diagnoseDocument(document);
		
		// 等待一点时间让诊断完成
		await delay(500);
		
		// 获取诊断问题
		const issues = diagnosticService.getIssues(jsonUri);
		
		// 验证是否检测到JSON语法问题
		const jsonSyntaxIssues = issues.filter(issue => 
			issue.message.includes('JSON') || issue.message.includes('json'));
		
		console.log(`JSON文件诊断测试通过，共检测到 ${issues.length} 个问题，其中 ${jsonSyntaxIssues.length} 个JSON语法问题`);
	});
});
