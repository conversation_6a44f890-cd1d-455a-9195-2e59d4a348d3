import * as vscode from 'vscode';

/**
 * Monaco编辑器配置
 */
export interface MonacoEditorConfig {
  /**
   * 编辑器内容
   */
  value: string;
  
  /**
   * 编辑器语言
   */
  language: string;
  
  /**
   * 是否只读
   */
  readOnly?: boolean;
  
  /**
   * 主题
   */
  theme?: 'vs' | 'vs-dark' | 'hc-black';
  
  /**
   * 其他Monaco编辑器选项
   */
  options?: any;
  
  /**
   * 是否启用智能提示
   */
  enableIntelliSense?: boolean;
}

/**
 * Monaco编辑器服务类
 * 负责Monaco编辑器的加载和配置
 */
export class MonacoService {
  private static instance: MonacoService | undefined;
  private _onConfigChanged = new vscode.EventEmitter<MonacoEditorConfig>();
  private _currentConfig: MonacoEditorConfig | undefined;
  
  readonly onConfigChanged = this._onConfigChanged.event;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): MonacoService {
    if (!MonacoService.instance) {
      MonacoService.instance = new MonacoService();
    }
    return MonacoService.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    this._currentConfig = {
      value: '',
      language: 'plaintext',
      readOnly: false,
      theme: 'vs-dark',
      enableIntelliSense: true,
      options: {
        automaticLayout: true,
        minimap: {
          enabled: false
        },
        scrollBeyondLastLine: false,
        fontFamily: 'Consolas, "Courier New", monospace',
        fontSize: 14,
        lineNumbers: 'on',
        // 智能提示相关配置
        quickSuggestions: true,
        suggestOnTriggerCharacters: true,
        acceptSuggestionOnEnter: "on",
        tabCompletion: "on",
        wordBasedSuggestions: "on",
        parameterHints: {
          enabled: true
        },
        snippetSuggestions: "inline"
      }
    };
  }
  
  /**
   * 配置Monaco编辑器
   * @param config 编辑器配置
   */
  public configureEditor(config: Partial<MonacoEditorConfig>): void {
    this._currentConfig = {
      ...this._currentConfig,
      ...config
    } as MonacoEditorConfig;
    
    this._onConfigChanged.fire(this._currentConfig);
  }
  
  /**
   * A获取当前编辑器配置
   */
  public getCurrentConfig(): MonacoEditorConfig {
    return this._currentConfig || {
      value: '',
      language: 'plaintext',
      theme: 'vs-dark'
    };
  }
  
  /**
   * 从活动编辑器获取内容和配置
   */
  public async loadFromActiveEditor(): Promise<MonacoEditorConfig | undefined> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return undefined;
    }
    
    const document = editor.document;
    const text = document.getText();
    const language = document.languageId;
    
    // 获取VSCode配置
    const vsCodeConfig = vscode.workspace.getConfiguration('editor');
    const lazyCodeConfig = vscode.workspace.getConfiguration('lazycode');
    
    const config: MonacoEditorConfig = {
      value: text,
      language,
      readOnly: false,
      theme: lazyCodeConfig.get('monacoEditorTheme') as ('vs' | 'vs-dark' | 'hc-black') || 'vs-dark',
      enableIntelliSense: true,
      options: {
        // 从VSCode编辑器配置中同步某些选项
        tabSize: vsCodeConfig.get('tabSize') || 4,
        insertSpaces: vsCodeConfig.get('insertSpaces') || true,
        detectIndentation: vsCodeConfig.get('detectIndentation') || true,
        wordWrap: vsCodeConfig.get('wordWrap') || 'off',
        autoIndent: vsCodeConfig.get('autoIndent') || 'advanced'
      }
    };
    
    this.configureEditor(config);
    return config;
  }
  
  /**
   * 应用编辑器内容到活动文档
   * @param content 编辑器内容
   */
  public async applyToActiveEditor(content: string): Promise<boolean> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return false;
    }
    
    const fullRange = new vscode.Range(
      new vscode.Position(0, 0),
      new vscode.Position(editor.document.lineCount, 0)
    );
    
    return editor.edit(editBuilder => {
      editBuilder.replace(fullRange, content);
    });
  }
  
  /**
   * 获取特定语言的额外配置
   * @param language 语言ID
   * @returns 特定语言的配置选项
   */
  public getLanguageSpecificOptions(language: string): any {
    // 根据不同语言提供不同的默认配置
    switch (language) {
      case 'javascript':
      case 'typescript':
        return {
          // JS/TS特定配置
          formatOnType: true,
          inlayHints: {
            enabled: true
          }
        };
      case 'html':
      case 'xml':
        return {
          // HTML特定配置
          autoClosingTags: true
        };
      case 'css':
      case 'scss':
      case 'less':
        return {
          // CSS特定配置
          colorDecorators: true
        };
      case 'json':
        return {
          // JSON特定配置
          schemaValidation: true
        };
      default:
        return {};
    }
  }
  
  /**
   * 获取Monaco加载脚本
   * 此脚本将被嵌入到WebView HTML中，用于加载Monaco编辑器
   */
  public getMonacoLoaderScript(): string {
    return `
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = \`https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/\${MONACO_VERSION}/min\`;

// Initialize monacoReady if it doesn't exist
window.monacoReady = window.monacoReady || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.monacoReady.promise = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = \`\${MONACO_CDN_BASE}/vs/loader.min.js\`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: \`\${MONACO_CDN_BASE}/vs\` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // 加载额外的语言服务
  require([
    "vs/language/typescript/tsWorker",
    "vs/language/json/jsonWorker",
    "vs/language/html/htmlWorker",
    "vs/language/css/cssWorker"
  ], () => {
    // 语言服务已加载，解析monaco promise
    if (monacoResolve) {
      monacoResolve(window.monaco);
    }
  });
}`;
  }
} 