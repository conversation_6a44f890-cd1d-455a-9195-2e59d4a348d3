import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { MemorySearch, MemorySearchOptions } from '../utils/MemorySearch';

/**
 * 记忆项类型枚举
 */
export enum MemoryItemType {
  /**
   * 代码片段
   */
  CODE_SNIPPET = 'code_snippet',
  
  /**
   * 编辑操作
   */
  EDIT_OPERATION = 'edit_operation',
  
  /**
   * 文件模板
   */
  FILE_TEMPLATE = 'file_template',
  
  /**
   * 自定义记忆
   */
  CUSTOM = 'custom'
}

/**
 * 记忆项接口
 */
export interface MemoryItem {
  /**
   * 记忆ID
   */
  id: string;
  
  /**
   * 记忆名称
   */
  name: string;
  
  /**
   * 记忆描述
   */
  description: string;
  
  /**
   * 记忆类型
   */
  type: MemoryItemType;
  
  /**
   * 记忆内容
   */
  content: string;
  
  /**
   * 适用的语言
   */
  languages: string[];
  
  /**
   * 标签
   */
  tags: string[];
  
  /**
   * 创建时间
   */
  createdAt: number;
  
  /**
   * 最后使用时间
   */
  lastUsedAt: number;
  
  /**
   * 使用次数
   */
  usageCount: number;
  
  /**
   * 星标状态
   */
  isStarred: boolean;
}

/**
 * 记忆服务类
 * 负责记忆的加载、保存、检索和应用
 */
export class MemoryService {
  private static instance: MemoryService | undefined;
  private _memories: Map<string, MemoryItem> = new Map();
  private _memoryDirectory: string = '';
  private _onMemoriesChanged = new vscode.EventEmitter<MemoryItem[]>();
  
  readonly onMemoriesChanged = this._onMemoriesChanged.event;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): MemoryService {
    if (!MemoryService.instance) {
      MemoryService.instance = new MemoryService();
    }
    return MemoryService.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    // 获取记忆存储目录路径
    this._memoryDirectory = path.join(
      vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '',
      '.lazycode',
      'memories'
    );
  }
  
  /**
   * 初始化记忆服务
   * @param context 扩展上下文
   */
  public async initialize(context: vscode.ExtensionContext): Promise<void> {
    // 确保记忆目录存在
    await this.ensureMemoryDirectory();
    
    // 加载记忆
    await this.loadMemories();
    
    // 注册记忆文件变化监听
    const memoryWatcher = vscode.workspace.createFileSystemWatcher(
      new vscode.RelativePattern(this._memoryDirectory, '*.json')
    );
    
    memoryWatcher.onDidChange(() => this.loadMemories());
    memoryWatcher.onDidCreate(() => this.loadMemories());
    memoryWatcher.onDidDelete(() => this.loadMemories());
    
    context.subscriptions.push(memoryWatcher);
  }
  
  /**
   * 确保记忆目录存在
   */
  private async ensureMemoryDirectory(): Promise<void> {
    try {
      // 检查记忆目录是否存在，不存在则创建
      if (!fs.existsSync(this._memoryDirectory)) {
        fs.mkdirSync(this._memoryDirectory, { recursive: true });
        console.log(`已创建记忆目录: ${this._memoryDirectory}`);
      }
    } catch (error) {
      console.error('创建记忆目录失败:', error);
      throw error;
    }
  }
  
  /**
   * 加载所有记忆
   */
  public async loadMemories(): Promise<MemoryItem[]> {
    try {
      // 清空当前记忆
      this._memories.clear();
      
      // 获取记忆文件列表
      const files = fs.readdirSync(this._memoryDirectory);
      const memoryFiles = files.filter(file => file.endsWith('.json'));
      
      // 处理每个记忆文件
      for (const file of memoryFiles) {
        const filePath = path.join(this._memoryDirectory, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        try {
          // 解析记忆对象
          const memoryItem: MemoryItem = JSON.parse(content);
          this._memories.set(memoryItem.id, memoryItem);
        } catch (parseError) {
          console.error(`解析记忆文件 ${file} 失败:`, parseError);
        }
      }
      
      // 触发记忆变化事件
      const memories = Array.from(this._memories.values());
      this._onMemoriesChanged.fire(memories);
      
      return memories;
    } catch (error) {
      console.error('加载记忆失败:', error);
      return [];
    }
  }
  
  /**
   * 获取所有记忆
   */
  public getAllMemories(): MemoryItem[] {
    return Array.from(this._memories.values());
  }
  
  /**
   * 获取指定ID的记忆
   * @param id 记忆ID
   */
  public getMemoryById(id: string): MemoryItem | undefined {
    return this._memories.get(id);
  }
  
  /**
   * 根据类型获取记忆
   * @param type 记忆类型
   */
  public getMemoriesByType(type: MemoryItemType): MemoryItem[] {
    return Array.from(this._memories.values()).filter(memory => memory.type === type);
  }
  
  /**
   * 根据标签获取记忆
   * @param tag 标签
   */
  public getMemoriesByTag(tag: string): MemoryItem[] {
    return Array.from(this._memories.values()).filter(memory => memory.tags.includes(tag));
  }
  
  /**
   * 根据语言获取记忆
   * @param language 语言ID
   */
  public getMemoriesByLanguage(language: string): MemoryItem[] {
    return Array.from(this._memories.values()).filter(
      memory => memory.languages.length === 0 || memory.languages.includes(language)
    );
  }
  
  /**
   * 获取星标记忆
   */
  public getStarredMemories(): MemoryItem[] {
    return Array.from(this._memories.values()).filter(memory => memory.isStarred);
  }
  
  /**
   * 搜索记忆
   * @param query 搜索关键词
   */
  public searchMemories(query: string): MemoryItem[] {
    const lowercaseQuery = query.toLowerCase();
    return Array.from(this._memories.values()).filter(memory => {
      return (
        memory.name.toLowerCase().includes(lowercaseQuery) ||
        memory.description.toLowerCase().includes(lowercaseQuery) ||
        memory.content.toLowerCase().includes(lowercaseQuery) ||
        memory.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
      );
    });
  }
  
  /**
   * 高级搜索记忆
   * @param options 搜索选项
   */
  public advancedSearch(options: MemorySearchOptions): MemoryItem[] {
    return MemorySearch.search(this.getAllMemories(), options);
  }
  
  /**
   * 按相关性搜索记忆
   * @param query 搜索关键词
   */
  public searchByRelevance(query: string): MemoryItem[] {
    return MemorySearch.sortByRelevance(this.getAllMemories(), query);
  }
  
  /**
   * 获取常用标签
   * @param limit 限制数量
   */
  public getCommonTags(limit: number = 10): {tag: string, count: number}[] {
    return MemorySearch.extractCommonTags(this.getAllMemories(), limit);
  }
  
  /**
   * 获取使用的编程语言
   */
  public getUsedLanguages(): {language: string, count: number}[] {
    return MemorySearch.extractLanguages(this.getAllMemories());
  }
  
  /**
   * 创建新记忆
   * @param memory 记忆对象
   */
  public async createMemory(memory: Omit<MemoryItem, 'id' | 'createdAt' | 'lastUsedAt' | 'usageCount'>): Promise<MemoryItem> {
    // 生成记忆ID
    const id = `memory-${Date.now()}`;
    
    // 创建完整记忆对象
    const newMemory: MemoryItem = {
      ...memory,
      id,
      createdAt: Date.now(),
      lastUsedAt: Date.now(),
      usageCount: 0
    };
    
    // 保存记忆文件
    await this.saveMemoryToFile(newMemory);
    
    // 添加到记忆集合
    this._memories.set(id, newMemory);
    
    // 触发记忆变化事件
    this._onMemoriesChanged.fire(this.getAllMemories());
    
    return newMemory;
  }
  
  /**
   * 更新记忆
   * @param id 记忆ID
   * @param memory 更新的记忆内容
   */
  public async updateMemory(id: string, memory: Partial<MemoryItem>): Promise<MemoryItem | undefined> {
    // 获取原记忆
    const existingMemory = this._memories.get(id);
    if (!existingMemory) {
      return undefined;
    }
    
    // 创建更新后的记忆对象
    const updatedMemory: MemoryItem = {
      ...existingMemory,
      ...memory
    };
    
    // 保存记忆文件
    await this.saveMemoryToFile(updatedMemory);
    
    // 更新记忆集合
    this._memories.set(id, updatedMemory);
    
    // 触发记忆变化事件
    this._onMemoriesChanged.fire(this.getAllMemories());
    
    return updatedMemory;
  }
  
  /**
   * 删除记忆
   * @param id 记忆ID
   */
  public async deleteMemory(id: string): Promise<boolean> {
    // 获取记忆
    const memory = this._memories.get(id);
    if (!memory) {
      return false;
    }
    
    // 删除记忆文件
    const filePath = path.join(this._memoryDirectory, `${id}.json`);
    try {
      fs.unlinkSync(filePath);
    } catch (error) {
      console.error(`删除记忆文件失败: ${filePath}`, error);
      return false;
    }
    
    // 从记忆集合中移除
    this._memories.delete(id);
    
    // 触发记忆变化事件
    this._onMemoriesChanged.fire(this.getAllMemories());
    
    return true;
  }
  
  /**
   * 保存记忆到文件
   * @param memory 记忆对象
   */
  private async saveMemoryToFile(memory: MemoryItem): Promise<void> {
    // 保存到文件
    const filePath = path.join(this._memoryDirectory, `${memory.id}.json`);
    fs.writeFileSync(filePath, JSON.stringify(memory, null, 2), 'utf8');
  }
  
  /**
   * 记录记忆使用
   * @param id 记忆ID
   */
  public async recordMemoryUsage(id: string): Promise<void> {
    const memory = this._memories.get(id);
    if (!memory) {
      return;
    }
    
    // 更新使用次数和最后使用时间
    memory.usageCount++;
    memory.lastUsedAt = Date.now();
    
    // 保存更新
    await this.saveMemoryToFile(memory);
  }
  
  /**
   * 切换记忆星标状态
   * @param id 记忆ID
   */
  public async toggleStarred(id: string): Promise<boolean> {
    const memory = this._memories.get(id);
    if (!memory) {
      return false;
    }
    
    // 切换星标状态
    memory.isStarred = !memory.isStarred;
    
    // 保存更新
    await this.saveMemoryToFile(memory);
    
    // 触发记忆变化事件
    this._onMemoriesChanged.fire(this.getAllMemories());
    
    return true;
  }
  
  /**
   * 从当前编辑器捕获记忆
   * @param name 记忆名称
   * @param description 记忆描述
   * @param type 记忆类型
   * @param tags 标签
   */
  public async captureFromEditor(
    name: string,
    description: string,
    type: MemoryItemType = MemoryItemType.CODE_SNIPPET,
    tags: string[] = []
  ): Promise<MemoryItem | undefined> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return undefined;
    }
    
    // 获取选中的文本，如果没有选中则获取整个文档
    const selection = editor.selection;
    const content = selection.isEmpty ? 
      editor.document.getText() : 
      editor.document.getText(selection);
    
    if (!content.trim()) {
      return undefined;
    }
    
    // 创建记忆
    const language = editor.document.languageId;
    return this.createMemory({
      name,
      description,
      type,
      content,
      languages: [language],
      tags,
      isStarred: false
    });
  }
  
  /**
   * 应用记忆到编辑器
   * @param id 记忆ID
   */
  public async applyToEditor(id: string): Promise<boolean> {
    const memory = this._memories.get(id);
    if (!memory) {
      return false;
    }
    
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return false;
    }
    
    // 插入记忆内容
    try {
      await editor.edit(editBuilder => {
        if (editor.selection.isEmpty) {
          // 如果没有选中文本，则在光标位置插入
          editBuilder.insert(editor.selection.active, memory.content);
        } else {
          // 如果选中了文本，则替换选中的文本
          editBuilder.replace(editor.selection, memory.content);
        }
      });
      
      // 记录使用
      await this.recordMemoryUsage(id);
      
      return true;
    } catch (error) {
      console.error('应用记忆失败:', error);
      return false;
    }
  }
  
  /**
   * 导出记忆
   * @param id 记忆ID
   * @param targetPath 导出目标路径
   */
  public async exportMemory(id: string, targetPath: string): Promise<boolean> {
    const memory = this._memories.get(id);
    if (!memory) {
      return false;
    }
    
    try {
      fs.writeFileSync(targetPath, JSON.stringify(memory, null, 2), 'utf8');
      return true;
    } catch (error) {
      console.error('导出记忆失败:', error);
      return false;
    }
  }
  
  /**
   * 导入记忆
   * @param sourcePath 导入源路径
   */
  public async importMemory(sourcePath: string): Promise<MemoryItem | undefined> {
    try {
      const content = fs.readFileSync(sourcePath, 'utf8');
      const memoryItem: MemoryItem = JSON.parse(content);
      
      // 生成新ID，避免冲突
      memoryItem.id = `memory-${Date.now()}`;
      
      // 保存记忆
      await this.saveMemoryToFile(memoryItem);
      
      // 添加到记忆集合
      this._memories.set(memoryItem.id, memoryItem);
      
      // 触发记忆变化事件
      this._onMemoriesChanged.fire(this.getAllMemories());
      
      return memoryItem;
    } catch (error) {
      console.error('导入记忆失败:', error);
      return undefined;
    }
  }
} 