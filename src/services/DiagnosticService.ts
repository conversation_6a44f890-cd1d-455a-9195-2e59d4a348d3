import * as vscode from 'vscode';
import { RulesService } from './RulesService';

/**
 * 诊断问题严重性枚举
 */
export enum DiagnosticSeverity {
  /**
   * 错误 - 需要立即修复的问题
   */
  ERROR = 'error',

  /**
   * 警告 - 可能导致问题的代码
   */
  WARNING = 'warning',

  /**
   * 提示 - 建议改进的地方
   */
  HINT = 'hint',

  /**
   * 信息 - 一般性信息
   */
  INFO = 'info'
}

/**
 * 诊断问题来源枚举
 */
export enum DiagnosticSource {
  /**
   * 规则检查
   */
  RULE = 'rule',

  /**
   * 语法分析
   */
  SYNTAX = 'syntax',

  /**
   * 样式检查
   */
  STYLE = 'style',

  /**
   * 性能检查
   */
  PERFORMANCE = 'performance',

  /**
   * 安全检查
   */
  SECURITY = 'security',

  /**
   * 其他来源
   */
  OTHER = 'other'
}

/**
 * 修复操作类型枚举
 */
export enum FixActionType {
  /**
   * 替换文本
   */
  REPLACE = 'replace',

  /**
   * 删除文本
   */
  DELETE = 'delete',

  /**
   * 插入文本
   */
  INSERT = 'insert',

  /**
   * 多重操作
   */
  MULTIPLE = 'multiple'
}

/**
 * 修复操作接口
 */
export interface FixAction {
  /**
   * 操作类型
   */
  type: FixActionType;

  /**
   * 开始位置
   */
  start: vscode.Position;

  /**
   * 结束位置
   */
  end: vscode.Position;

  /**
   * 要替换或插入的文本
   */
  text?: string;

  /**
   * 多重操作列表
   */
  actions?: FixAction[];
}

/**
 * 修复建议接口
 */
export interface FixSuggestion {
  /**
   * 建议ID
   */
  id: string;

  /**
   * 建议标题
   */
  title: string;

  /**
   * 建议描述
   */
  description: string;

  /**
   * 修复操作
   */
  action: FixAction;

  /**
   * 修复前代码预览
   */
  beforePreview?: string;

  /**
   * 修复后代码预览
   */
  afterPreview?: string;
}

/**
 * 诊断问题接口
 */
export interface DiagnosticIssue {
  /**
   * 问题ID
   */
  id: string;

  /**
   * 问题消息
   */
  message: string;

  /**
   * 问题严重性
   */
  severity: DiagnosticSeverity;

  /**
   * 问题来源
   */
  source: DiagnosticSource;

  /**
   * 问题范围
   */
  range: vscode.Range;

  /**
   * 问题所在文件
   */
  file: vscode.Uri;

  /**
   * 问题修复建议
   */
  fixes: FixSuggestion[];

  /**
   * 问题相关规则ID
   */
  ruleId?: string;

  /**
   * 问题代码上下文
   */
  context?: string;

  /**
   * 问题发现时间
   */
  timestamp: number;
}

/**
 * 诊断服务类
 * 负责检测代码问题并生成修复建议
 */
export class DiagnosticService {
  private static instance: DiagnosticService | undefined;
  private _diagnosticCollection: vscode.DiagnosticCollection;
  private _issues: Map<string, DiagnosticIssue[]> = new Map();
  private _onDiagnosticsChanged = new vscode.EventEmitter<DiagnosticIssue[]>();

  readonly onDiagnosticsChanged = this._onDiagnosticsChanged.event;

  /**
   * 获取单例实例
   */
  public static getInstance(): DiagnosticService {
    if (!DiagnosticService.instance) {
      DiagnosticService.instance = new DiagnosticService();
    }
    return DiagnosticService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    this._diagnosticCollection = vscode.languages.createDiagnosticCollection('lazycode');
  }

  /**
   * 初始化诊断服务
   * @param context 扩展上下文
   */
  public async initialize(context: vscode.ExtensionContext): Promise<void> {
    // 注册诊断集合
    context.subscriptions.push(this._diagnosticCollection);

    // 监听文档变化
    context.subscriptions.push(
      vscode.workspace.onDidChangeTextDocument(this.onDocumentChanged, this)
    );

    // 监听活动编辑器变化
    context.subscriptions.push(
      vscode.window.onDidChangeActiveTextEditor(this.onActiveEditorChanged, this)
    );

    // 初始诊断当前打开的文档
    if (vscode.window.activeTextEditor) {
      await this.diagnoseDocument(vscode.window.activeTextEditor.document);
    }
  }

  /**
   * 处理文档变化事件
   */
  private async onDocumentChanged(event: vscode.TextDocumentChangeEvent): Promise<void> {
    // 文档变化时重新诊断
    await this.diagnoseDocument(event.document);
  }

  /**
   * 处理活动编辑器变化事件
   */
  private async onActiveEditorChanged(editor: vscode.TextEditor | undefined): Promise<void> {
    if (editor) {
      // 编辑器切换时诊断当前文档
      await this.diagnoseDocument(editor.document);
    }
  }

  /**
   * 诊断文档
   * @param document 要诊断的文档
   */
  public async diagnoseDocument(document: vscode.TextDocument): Promise<DiagnosticIssue[]> {
    // 清除旧的诊断信息
    this._diagnosticCollection.delete(document.uri);

    // 清除旧的问题记录
    this._issues.delete(document.uri.toString());

    // 获取文档内容和语言
    const content = document.getText();
    const language = document.languageId;

    // 创建诊断列表
    const diagnostics: vscode.Diagnostic[] = [];

    // 创建问题列表
    const issues: DiagnosticIssue[] = [];

    try {
      // 通过规则进行诊断
      const ruleIssues = await this.diagnoseByRules(document, content, language);
      issues.push(...ruleIssues);

      // 通过语法分析进行诊断
      const syntaxIssues = await this.diagnoseBySyntax(document, content, language);
      issues.push(...syntaxIssues);

      // 通过样式检查进行诊断
      const styleIssues = await this.diagnoseByStyle(document, content, language);
      issues.push(...styleIssues);

      // 将问题转换为VSCode诊断
      issues.forEach(issue => {
        const diagnostic = new vscode.Diagnostic(
          issue.range,
          issue.message,
          this.getSeverity(issue.severity)
        );

        diagnostic.source = 'LazyCode';
        diagnostic.code = issue.id;

        diagnostics.push(diagnostic);
      });

      // 设置诊断结果
      this._diagnosticCollection.set(document.uri, diagnostics);

      // 保存问题列表
      this._issues.set(document.uri.toString(), issues);

      // 触发诊断变化事件
      this._onDiagnosticsChanged.fire(issues);

      return issues;
    } catch (error) {
      console.error('诊断文档失败:', error);
      return [];
    }
  }

  /**
   * 通过规则进行诊断
   * @param document 文档
   * @param content 文档内容
   * @param language 文档语言
   */
  private async diagnoseByRules(
    document: vscode.TextDocument,
    content: string,
    language: string
  ): Promise<DiagnosticIssue[]> {
    const issues: DiagnosticIssue[] = [];

    try {
      // 获取规则服务
      const rulesService = RulesService.getInstance();

      // 获取适用于当前语言的规则
      const rules = rulesService.getRulesByLanguage(language);

      // 应用每个规则进行检查
      for (const rule of rules) {
        // 跳过非诊断类规则
        if (rule.type !== 'analysis') {
          continue;
        }

        // 检查规则模式是否匹配
        if (rule.pattern && rule.pattern.trim() !== '') {
          try {
            const regex = new RegExp(rule.pattern, 'g');
            let match;

            // 查找所有匹配
            while ((match = regex.exec(content)) !== null) {
              // 获取匹配范围
              const startPos = document.positionAt(match.index);
              const endPos = document.positionAt(match.index + match[0].length);
              const range = new vscode.Range(startPos, endPos);

              // 获取上下文
              const lineStart = document.lineAt(startPos.line).text;

              // 创建修复建议
              const fixSuggestion: FixSuggestion = {
                id: `fix-${rule.id}-${Date.now()}`,
                title: `应用规则 "${rule.name}" 修复`,
                description: rule.description,
                action: {
                  type: FixActionType.REPLACE,
                  start: startPos,
                  end: endPos,
                  text: rule.replacement ? match[0].replace(regex, rule.replacement) : undefined
                },
                beforePreview: match[0],
                afterPreview: rule.replacement ? match[0].replace(regex, rule.replacement) : undefined
              };

              // 创建诊断问题
              const issue: DiagnosticIssue = {
                id: `issue-${rule.id}-${Date.now()}`,
                message: rule.description,
                severity: this.mapRuleSeverity(rule.severity),
                source: DiagnosticSource.RULE,
                range,
                file: document.uri,
                fixes: [fixSuggestion],
                ruleId: rule.id,
                context: lineStart,
                timestamp: Date.now()
              };

              issues.push(issue);
            }
          } catch (error) {
            console.error(`应用规则 ${rule.id} 时出错:`, error);
          }
        }
      }
    } catch (error) {
      console.error('规则诊断失败:', error);
    }

    return issues;
  }

  /**
   * 通过智能上下文分析生成更精确的修复建议
   * @param document 文档
   * @param position 位置
   * @param range 范围
   * @param message 消息
   * @param severity 严重性
   * @param source 来源
   */
  private createContextAwareFixSuggestion(
    document: vscode.TextDocument,
    position: vscode.Position,
    range: vscode.Range,
    message: string,
    severity: DiagnosticSeverity,
    source: DiagnosticSource
  ): FixSuggestion[] {
    const suggestions: FixSuggestion[] = [];
    const line = document.lineAt(position.line).text;
    const prefix = line.substring(0, position.character);
    const suffix = line.substring(position.character);

    // 根据不同类型的问题生成更智能的修复建议
    if (message.includes('未闭合') || message.includes('缺少')) {
      // 处理括号、引号等未闭合问题
      if (message.includes('括号') || message.includes('花括号')) {
        // 生成更智能的闭合建议
        const openChar = document.getText(range);
        let closeChar = '';

        if (openChar === '(') {
          closeChar = ')';
        } else if (openChar === '{') {
          closeChar = '}';
        } else if (openChar === '[') {
          closeChar = ']';
        }

        // 分析当前代码块结构，确定最佳插入位置
        const endOfLine = new vscode.Position(position.line, line.length);
        const endOfLineRange = new vscode.Range(position, endOfLine);
        const lineAfterPosition = document.getText(endOfLineRange);

        // 检查行尾是否已有内容，如果有则在行尾插入，否则插入到内容后面
        if (lineAfterPosition.trim().length > 0) {
          // 在行尾添加闭合字符
          suggestions.push({
            id: `fix-close-${Date.now()}`,
            title: `在行尾添加 ${closeChar}`,
            description: `添加缺失的闭合字符 ${closeChar}`,
            action: {
              type: FixActionType.INSERT,
              start: endOfLine,
              end: endOfLine,
              text: closeChar
            },
            beforePreview: line,
            afterPreview: line + closeChar
          });
        }

        // 提供插入到当前位置后的选项
        suggestions.push({
          id: `fix-close-inline-${Date.now()}`,
          title: `在当前位置后添加 ${closeChar}`,
          description: `在当前位置后添加闭合字符 ${closeChar}`,
          action: {
            type: FixActionType.INSERT,
            start: position.translate(0, 1),
            end: position.translate(0, 1),
            text: closeChar
          },
          beforePreview: line,
          afterPreview: prefix + openChar + closeChar + suffix.substring(1)
        });
      }
    } else if (message.includes('拼写错误') || message.includes('应为')) {
      // 更智能的拼写修复已在原有代码中实现
      // 这里不需要添加额外内容
    }

    return suggestions;
  }

  /**
   * 通过语法分析进行诊断
   * @param document 文档
   * @param content 文档内容
   * @param language 文档语言
   */
  private async diagnoseBySyntax(
    document: vscode.TextDocument,
    content: string,
    language: string
  ): Promise<DiagnosticIssue[]> {
    const issues: DiagnosticIssue[] = [];

    // 根据不同语言实现不同的语法检查逻辑
    if (language === 'javascript' || language === 'typescript') {
      // 检查未闭合的括号、引号等
      const bracketPairs = [
        { open: '(', close: ')' },
        { open: '{', close: '}' },
        { open: '[', close: ']' }
      ];

      const quotePairs = [
        { open: '"', close: '"' },
        { open: "'", close: "'" },
        { open: '`', close: '`' }
      ];

      // 检查括号匹配
      for (const pair of bracketPairs) {
        const stack: number[] = [];

        for (let i = 0; i < content.length; i++) {
          if (content[i] === pair.open) {
            stack.push(i);
          } else if (content[i] === pair.close) {
            if (stack.length === 0) {
              // 多余的闭合括号
              const pos = document.positionAt(i);
              const range = new vscode.Range(pos, pos.translate(0, 1));

              const fixSuggestion: FixSuggestion = {
                id: `fix-bracket-${Date.now()}`,
                title: `删除多余的 ${pair.close}`,
                description: `删除多余的闭合字符 ${pair.close}`,
                action: {
                  type: FixActionType.DELETE,
                  start: pos,
                  end: pos.translate(0, 1)
                }
              };

              const issue: DiagnosticIssue = {
                id: `issue-bracket-${Date.now()}`,
                message: `多余的闭合字符 ${pair.close}`,
                severity: DiagnosticSeverity.ERROR,
                source: DiagnosticSource.SYNTAX,
                range,
                file: document.uri,
                fixes: [fixSuggestion],
                context: document.lineAt(pos.line).text,
                timestamp: Date.now()
              };

              issues.push(issue);
            } else {
              stack.pop();
            }
          }
        }

        // 查找未闭合的括号
        for (const pos of stack) {
          const docPos = document.positionAt(pos);
          const range = new vscode.Range(docPos, docPos.translate(0, 1));

          const fixSuggestion: FixSuggestion = {
            id: `fix-bracket-${Date.now()}`,
            title: `插入 ${pair.close}`,
            description: `在适当位置插入闭合字符 ${pair.close}`,
            action: {
              type: FixActionType.INSERT,
              start: document.positionAt(content.length),
              end: document.positionAt(content.length),
              text: pair.close
            }
          };

          const issue: DiagnosticIssue = {
            id: `issue-bracket-${Date.now()}`,
            message: `未闭合的 ${pair.open}`,
            severity: DiagnosticSeverity.ERROR,
            source: DiagnosticSource.SYNTAX,
            range,
            file: document.uri,
            fixes: [fixSuggestion],
            context: document.lineAt(docPos.line).text,
            timestamp: Date.now()
          };

          issues.push(issue);
        }
      }

      // 检查引号匹配
      for (const pair of quotePairs) {
        let isInQuote = false;
        let quoteStartPos = -1;
        let isPrevEscape = false;

        for (let i = 0; i < content.length; i++) {
          // 检查是否为转义字符
          if (content[i] === '\\') {
            isPrevEscape = !isPrevEscape;
            continue;
          }

          if (content[i] === pair.open && !isPrevEscape) {
            if (isInQuote) {
              // 引号已正确闭合
              isInQuote = false;
            } else {
              // 开始引号
              isInQuote = true;
              quoteStartPos = i;
            }
          }

          isPrevEscape = false;
        }

        // 如果最后还在引号中，说明未闭合
        if (isInQuote && quoteStartPos !== -1) {
          const docPos = document.positionAt(quoteStartPos);
          const range = new vscode.Range(docPos, docPos.translate(0, 1));

          const fixSuggestion: FixSuggestion = {
            id: `fix-quote-${Date.now()}`,
            title: `插入 ${pair.close}`,
            description: `在适当位置插入闭合引号 ${pair.close}`,
            action: {
              type: FixActionType.INSERT,
              start: document.positionAt(content.length),
              end: document.positionAt(content.length),
              text: pair.close
            }
          };

          const issue: DiagnosticIssue = {
            id: `issue-quote-${Date.now()}`,
            message: `未闭合的引号 ${pair.open}`,
            severity: DiagnosticSeverity.ERROR,
            source: DiagnosticSource.SYNTAX,
            range,
            file: document.uri,
            fixes: [fixSuggestion],
            context: document.lineAt(docPos.line).text,
            timestamp: Date.now()
          };

          issues.push(issue);
        }
      }

      // 检查可能的拼写错误（常见变量名和关键字）
      const jsKeywords = ['function', 'return', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'switch', 'case'];
      const jsLines = content.split('\n');

      for (let i = 0; i < jsLines.length; i++) {
        const line = jsLines[i];

        // 检查常见的拼写错误
        const misspelledMatches = line.match(/\b(funtion|functoin|reutrn|retrun|cosnt|conts|lte|elsee|whiel)\b/g);

        if (misspelledMatches) {
          for (const misspelled of misspelledMatches) {
            let correctWord = '';

            // 简单的拼写纠正映射
            switch (misspelled) {
              case 'funtion':
              case 'functoin':
                correctWord = 'function';
                break;
              case 'reutrn':
              case 'retrun':
                correctWord = 'return';
                break;
              case 'cosnt':
              case 'conts':
                correctWord = 'const';
                break;
              case 'lte':
                correctWord = 'let';
                break;
              case 'elsee':
                correctWord = 'else';
                break;
              case 'whiel':
                correctWord = 'while';
                break;
            }

            if (correctWord) {
              const startIndex = line.indexOf(misspelled);
              const startPos = new vscode.Position(i, startIndex);
              const endPos = new vscode.Position(i, startIndex + misspelled.length);
              const range = new vscode.Range(startPos, endPos);

              const fixSuggestion: FixSuggestion = {
                id: `fix-spell-${Date.now()}`,
                title: `将 "${misspelled}" 更正为 "${correctWord}"`,
                description: `修正拼写错误`,
                action: {
                  type: FixActionType.REPLACE,
                  start: startPos,
                  end: endPos,
                  text: correctWord
                },
                beforePreview: misspelled,
                afterPreview: correctWord
              };

              const issue: DiagnosticIssue = {
                id: `issue-spell-${Date.now()}`,
                message: `可能的拼写错误: "${misspelled}"，应为 "${correctWord}"`,
                severity: DiagnosticSeverity.WARNING,
                source: DiagnosticSource.SYNTAX,
                range,
                file: document.uri,
                fixes: [fixSuggestion],
                context: line,
                timestamp: Date.now()
              };

              issues.push(issue);
            }
          }
        }
      }

      // 检查冗余分号和逗号
      const semicolonLines = content.split('\n');
      for (let i = 0; i < semicolonLines.length; i++) {
        const line = semicolonLines[i];

        // 检查冗余分号，如 let a = 5;;
        const redundantSemicolonMatch = line.match(/;{2,}/g);
        if (redundantSemicolonMatch) {
          for (const match of redundantSemicolonMatch) {
            const startIndex = line.indexOf(match);
            const startPos = new vscode.Position(i, startIndex);
            const endPos = new vscode.Position(i, startIndex + match.length);
            const range = new vscode.Range(startPos, endPos);

            const fixSuggestion: FixSuggestion = {
              id: `fix-redundant-semicolon-${Date.now()}`,
              title: '删除冗余分号',
              description: '删除多余的分号',
              action: {
                type: FixActionType.REPLACE,
                start: startPos,
                end: endPos,
                text: ';' // 替换为单个分号
              },
              beforePreview: match,
              afterPreview: ';'
            };

            const issue: DiagnosticIssue = {
              id: `issue-redundant-semicolon-${Date.now()}`,
              message: '冗余分号',
              severity: DiagnosticSeverity.WARNING,
              source: DiagnosticSource.SYNTAX,
              range,
              file: document.uri,
              fixes: [fixSuggestion],
              context: line,
              timestamp: Date.now()
            };

            issues.push(issue);
          }
        }

        // 检查对象或数组末尾的冗余逗号，如 {a: 1, b: 2,}
        const redundantCommaMatch = line.match(/,(\s*[\]\}])/g);
        if (redundantCommaMatch) {
          for (const match of redundantCommaMatch) {
            const startIndex = line.indexOf(match);
            const startPos = new vscode.Position(i, startIndex);
            const endPos = new vscode.Position(i, startIndex + 1); // 只删除逗号
            const range = new vscode.Range(startPos, endPos);

            const fixSuggestion: FixSuggestion = {
              id: `fix-redundant-comma-${Date.now()}`,
              title: '删除冗余逗号',
              description: '删除多余的逗号',
              action: {
                type: FixActionType.DELETE,
                start: startPos,
                end: endPos
              },
              beforePreview: match,
              afterPreview: match.substring(1) // 没有逗号的版本
            };

            const issue: DiagnosticIssue = {
              id: `issue-redundant-comma-${Date.now()}`,
              message: '在对象或数组末尾存在冗余逗号',
              severity: DiagnosticSeverity.HINT,
              source: DiagnosticSource.STYLE,
              range,
              file: document.uri,
              fixes: [fixSuggestion],
              context: line,
              timestamp: Date.now()
            };

            issues.push(issue);
          }
        }
      }
    } else if (language === 'html' || language === 'xml' || language === 'vue') {
      // HTML/XML标签匹配检查
      const tagStack: {name: string, pos: number}[] = [];
      const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9-_:.]*)\s*[^>]*>/g;
      const selfClosingTags = new Set(['img', 'br', 'hr', 'input', 'meta', 'link', 'area', 'base', 'col', 'embed', 'keygen', 'param', 'source', 'track', 'wbr']);

      let match;
      while ((match = tagRegex.exec(content)) !== null) {
        const tagContent = match[0];
        const tagName = match[1];
        const tagPos = match.index;

        if (tagContent.startsWith('</')) {
          // 关闭标签
          if (tagStack.length === 0) {
            // 多余的关闭标签
            const pos = document.positionAt(tagPos);
            const endPos = document.positionAt(tagPos + tagContent.length);
            const range = new vscode.Range(pos, endPos);

            const fixSuggestion: FixSuggestion = {
              id: `fix-html-tag-${Date.now()}`,
              title: `删除多余的闭合标签 </${tagName}>`,
              description: `删除多余的HTML闭合标签`,
              action: {
                type: FixActionType.DELETE,
                start: pos,
                end: endPos
              }
            };

            const issue: DiagnosticIssue = {
              id: `issue-html-tag-${Date.now()}`,
              message: `多余的闭合标签 </${tagName}>`,
              severity: DiagnosticSeverity.ERROR,
              source: DiagnosticSource.SYNTAX,
              range,
              file: document.uri,
              fixes: [fixSuggestion],
              context: document.lineAt(pos.line).text,
              timestamp: Date.now()
            };

            issues.push(issue);
          } else {
            // 检查是否匹配栈顶的开始标签
            const lastTag = tagStack.pop();
            if (lastTag && lastTag.name !== tagName) {
              // 标签不匹配
              const pos = document.positionAt(tagPos);
              const endPos = document.positionAt(tagPos + tagContent.length);
              const range = new vscode.Range(pos, endPos);

              const fixSuggestion: FixSuggestion = {
                id: `fix-html-tag-mismatch-${Date.now()}`,
                title: `将 </${tagName}> 更改为 </${lastTag.name}>`,
                description: `修正HTML标签不匹配问题`,
                action: {
                  type: FixActionType.REPLACE,
                  start: pos,
                  end: endPos,
                  text: `</${lastTag.name}>`
                },
                beforePreview: `</${tagName}>`,
                afterPreview: `</${lastTag.name}>`
              };

              const issue: DiagnosticIssue = {
                id: `issue-html-tag-mismatch-${Date.now()}`,
                message: `HTML标签不匹配: 期望 </>${lastTag.name}>, 实际为 </>${tagName}>`,
                severity: DiagnosticSeverity.ERROR,
                source: DiagnosticSource.SYNTAX,
                range,
                file: document.uri,
                fixes: [fixSuggestion],
                context: document.lineAt(pos.line).text,
                timestamp: Date.now()
              };

              issues.push(issue);

              // 将标签放回栈中，因为它仍然需要闭合
              tagStack.push(lastTag);
            }
          }
        } else if (!tagContent.endsWith('/>') && !selfClosingTags.has(tagName.toLowerCase())) {
          // 开始标签，如果不是自闭合标签则压入栈
          tagStack.push({ name: tagName, pos: tagPos });
        }
      }

      // 检查未闭合的标签
      for (const tag of tagStack) {
        const pos = document.positionAt(tag.pos);
        const range = new vscode.Range(pos, pos.translate(0, tag.name.length + 2)); // +2 for < and possible space

        const fixSuggestion: FixSuggestion = {
          id: `fix-html-unclosed-${Date.now()}`,
          title: `在文件末尾添加闭合标签 </${tag.name}>`,
          description: `添加缺失的HTML闭合标签`,
          action: {
            type: FixActionType.INSERT,
            start: document.positionAt(content.length),
            end: document.positionAt(content.length),
            text: `\n</${tag.name}>`
          }
        };

        const issue: DiagnosticIssue = {
          id: `issue-html-unclosed-${Date.now()}`,
          message: `未闭合的HTML标签: <${tag.name}>`,
          severity: DiagnosticSeverity.ERROR,
          source: DiagnosticSource.SYNTAX,
          range,
          file: document.uri,
          fixes: [fixSuggestion],
          context: document.lineAt(pos.line).text,
          timestamp: Date.now()
        };

        issues.push(issue);
      }
    } else if (language === 'css' || language === 'scss' || language === 'less') {
      // CSS语法检查
      // 检查未闭合的花括号
      const stack: number[] = [];

      for (let i = 0; i < content.length; i++) {
        if (content[i] === '{') {
          stack.push(i);
        } else if (content[i] === '}') {
          if (stack.length === 0) {
            // 多余的闭合花括号
            const pos = document.positionAt(i);
            const range = new vscode.Range(pos, pos.translate(0, 1));

            const fixSuggestion: FixSuggestion = {
              id: `fix-css-brace-${Date.now()}`,
              title: `删除多余的 }`,
              description: `删除多余的闭合花括号`,
              action: {
                type: FixActionType.DELETE,
                start: pos,
                end: pos.translate(0, 1)
              }
            };

            const issue: DiagnosticIssue = {
              id: `issue-css-brace-${Date.now()}`,
              message: `多余的闭合花括号 }`,
              severity: DiagnosticSeverity.ERROR,
              source: DiagnosticSource.SYNTAX,
              range,
              file: document.uri,
              fixes: [fixSuggestion],
              context: document.lineAt(pos.line).text,
              timestamp: Date.now()
            };

            issues.push(issue);
          } else {
            stack.pop();
          }
        }
      }

      // 查找未闭合的花括号
      for (const pos of stack) {
        const docPos = document.positionAt(pos);
        const range = new vscode.Range(docPos, docPos.translate(0, 1));

        const fixSuggestion: FixSuggestion = {
          id: `fix-css-brace-${Date.now()}`,
          title: `插入 }`,
          description: `在文件末尾插入闭合花括号`,
          action: {
            type: FixActionType.INSERT,
            start: document.positionAt(content.length),
            end: document.positionAt(content.length),
            text: '}'
          }
        };

        const issue: DiagnosticIssue = {
          id: `issue-css-brace-${Date.now()}`,
          message: `未闭合的花括号 {`,
          severity: DiagnosticSeverity.ERROR,
          source: DiagnosticSource.SYNTAX,
          range,
          file: document.uri,
          fixes: [fixSuggestion],
          context: document.lineAt(docPos.line).text,
          timestamp: Date.now()
        };

        issues.push(issue);
      }

      // 检查缺少分号的规则
      const lines = content.split('\n');
      const propertyRegex = /^\s*([a-zA-Z-]+)\s*:\s*([^;{]+)$/;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const match = line.match(propertyRegex);

        if (match && !line.trim().endsWith(';') && !line.trim().endsWith('{')) {
          const property = match[1];
          const value = match[2].trim();

          // 确保这不是嵌套规则（SASS/LESS）的开始
          const nextLine = i + 1 < lines.length ? lines[i + 1] : '';
          if (!nextLine.trim().startsWith('{')) {
            const startPos = new vscode.Position(i, line.length);
            const endPos = new vscode.Position(i, line.length);
            const range = new vscode.Range(startPos, endPos);

            const fixSuggestion: FixSuggestion = {
              id: `fix-css-semicolon-${Date.now()}`,
              title: `添加缺失的分号`,
              description: `在CSS属性声明后添加分号`,
              action: {
                type: FixActionType.INSERT,
                start: startPos,
                end: endPos,
                text: ';'
              },
              beforePreview: `${property}: ${value}`,
              afterPreview: `${property}: ${value};`
            };

            const issue: DiagnosticIssue = {
              id: `issue-css-semicolon-${Date.now()}`,
              message: `缺少分号: ${property}: ${value}`,
              severity: DiagnosticSeverity.WARNING,
              source: DiagnosticSource.SYNTAX,
              range,
              file: document.uri,
              fixes: [fixSuggestion],
              context: line,
              timestamp: Date.now()
            };

            issues.push(issue);
          }
        }
      }
    } else if (language === 'python') {
      // 检查Python缩进问题
      const pythonLines = content.split('\n');
      const indentationSpaces = 4; // 标准Python缩进为4个空格

      let previousIndentation = 0;
      for (let i = 0; i < pythonLines.length; i++) {
        const line = pythonLines[i].replace(/\t/g, ' '.repeat(indentationSpaces)); // 将Tab转换为空格

        // 跳过空行和注释行
        if (line.trim() === '' || line.trim().startsWith('#')) {
          continue;
        }

        // 计算当前行的缩进量
        const currentIndentation = line.length - line.trimLeft().length;

        // 检查缩进是否为4的倍数
        if (currentIndentation % indentationSpaces !== 0) {
          const startPos = new vscode.Position(i, 0);
          const endPos = new vscode.Position(i, currentIndentation);
          const range = new vscode.Range(startPos, endPos);

          // 计算正确的缩进
          const correctIndentation = Math.round(currentIndentation / indentationSpaces) * indentationSpaces;

          const fixSuggestion: FixSuggestion = {
            id: `fix-python-indent-${Date.now()}`,
            title: '修正Python缩进',
            description: '将缩进调整为4的倍数',
            action: {
              type: FixActionType.REPLACE,
              start: startPos,
              end: endPos,
              text: ' '.repeat(correctIndentation)
            },
            beforePreview: line,
            afterPreview: ' '.repeat(correctIndentation) + line.trimLeft()
          };

          const issue: DiagnosticIssue = {
            id: `issue-python-indent-${Date.now()}`,
            message: '不一致的Python缩进（应为4的倍数）',
            severity: DiagnosticSeverity.ERROR,
            source: DiagnosticSource.SYNTAX,
            range,
            file: document.uri,
            fixes: [fixSuggestion],
            context: line,
            timestamp: Date.now()
          };

          issues.push(issue);
        }

        // 检查Python中的未闭合括号
        const brackets = [
          { open: '(', close: ')' },
          { open: '{', close: '}' },
          { open: '[', close: ']' }
        ];

        for (const pair of brackets) {
          let openCount = 0;
          let closeCount = 0;

          // 计算当前行中括号的数量
          for (let j = 0; j < line.length; j++) {
            if (line[j] === pair.open) {
              openCount++;
            } else if (line[j] === pair.close) {
              closeCount++;
            }
          }

          // 如果开括号比闭括号多，这可能是多行语句的开始
          // 这里我们只提示确定的问题，如在普通语句中未闭合括号
          if (openCount > closeCount && !line.trim().endsWith('\\') &&
              !line.trim().endsWith(':') && i + 1 < pythonLines.length &&
              pythonLines[i + 1].trim() === '') {

            // 在非多行语句中发现未闭合括号
            const startPos = new vscode.Position(i, line.lastIndexOf(pair.open));
            const endPos = new vscode.Position(i, line.lastIndexOf(pair.open) + 1);
            const range = new vscode.Range(startPos, endPos);

            const fixSuggestion: FixSuggestion = {
              id: `fix-python-bracket-${Date.now()}`,
              title: `在行尾添加 ${pair.close}`,
              description: `添加缺失的闭合字符 ${pair.close}`,
              action: {
                type: FixActionType.INSERT,
                start: new vscode.Position(i, line.length),
                end: new vscode.Position(i, line.length),
                text: pair.close
              },
              beforePreview: line,
              afterPreview: line + pair.close
            };

            const issue: DiagnosticIssue = {
              id: `issue-python-bracket-${Date.now()}`,
              message: `可能未闭合的 ${pair.open}`,
              severity: DiagnosticSeverity.WARNING,
              source: DiagnosticSource.SYNTAX,
              range,
              file: document.uri,
              fixes: [fixSuggestion],
              context: line,
              timestamp: Date.now()
            };

            issues.push(issue);
          }
        }

        previousIndentation = currentIndentation;
      }
    } else if (language === 'markdown') {
      // 检查未闭合的代码块
      const codeBlockStartRegex = /^```([a-zA-Z0-9]*)/;
      const codeBlockEndRegex = /^```\s*$/;
      const mdLines = content.split('\n');
      const codeBlockStarts: number[] = [];

      for (let i = 0; i < mdLines.length; i++) {
        const line = mdLines[i];
        if (codeBlockStartRegex.test(line) && !codeBlockEndRegex.test(line)) {
          codeBlockStarts.push(i);
        } else if (codeBlockEndRegex.test(line) && codeBlockStarts.length > 0) {
          codeBlockStarts.pop(); // 代码块已闭合
        }
      }

      // 处理未闭合的代码块
      for (const lineNum of codeBlockStarts) {
        const startPos = new vscode.Position(lineNum, 0);
        const endPos = new vscode.Position(lineNum, mdLines[lineNum].length);
        const range = new vscode.Range(startPos, endPos);

        const fixSuggestion: FixSuggestion = {
          id: `fix-markdown-codeblock-${Date.now()}`,
          title: '添加闭合代码块标记',
          description: '在文档末尾添加闭合代码块标记 ```',
          action: {
            type: FixActionType.INSERT,
            start: document.positionAt(content.length),
            end: document.positionAt(content.length),
            text: '\n```'
          },
          beforePreview: mdLines[lineNum],
          afterPreview: mdLines[lineNum] + '\n// ...\n```'
        };

        const issue: DiagnosticIssue = {
          id: `issue-markdown-codeblock-${Date.now()}`,
          message: '未闭合的Markdown代码块',
          severity: DiagnosticSeverity.WARNING,
          source: DiagnosticSource.SYNTAX,
          range,
          file: document.uri,
          fixes: [fixSuggestion],
          context: mdLines[lineNum],
          timestamp: Date.now()
        };

        issues.push(issue);
      }

      // 检查空链接
      const emptyLinkRegex = /\[([^\]]+)\]\(\s*\)/g;
      for (let i = 0; i < mdLines.length; i++) {
        const line = mdLines[i];
        let match;

        while ((match = emptyLinkRegex.exec(line)) !== null) {
          const linkText = match[1];
          const startPos = new vscode.Position(i, match.index);
          const endPos = new vscode.Position(i, match.index + match[0].length);
          const range = new vscode.Range(startPos, endPos);

          const fixSuggestion: FixSuggestion = {
            id: `fix-markdown-emptylink-${Date.now()}`,
            title: '添加链接URL',
            description: '在空链接中添加URL占位符',
            action: {
              type: FixActionType.REPLACE,
              start: startPos,
              end: endPos,
              text: `[${linkText}](https://example.com)`
            },
            beforePreview: match[0],
            afterPreview: `[${linkText}](https://example.com)`
          };

          const issue: DiagnosticIssue = {
            id: `issue-markdown-emptylink-${Date.now()}`,
            message: `空链接: [${linkText}]()`,
            severity: DiagnosticSeverity.WARNING,
            source: DiagnosticSource.SYNTAX,
            range,
            file: document.uri,
            fixes: [fixSuggestion],
            context: line,
            timestamp: Date.now()
          };

          issues.push(issue);
        }
      }
    } else if (language === 'json' || language === 'jsonc') {
      // 检查JSON语法问题
      try {
        // 尝试解析JSON
        JSON.parse(content);
      } catch (e) {
        if (e instanceof SyntaxError) {
          // 得到错误消息和位置
          const errorMessage = e.message;

          // 尝试从错误消息中提取位置信息
          const positionMatch = errorMessage.match(/at position (\d+)/);
          if (positionMatch) {
            const errorPosition = parseInt(positionMatch[1]);
            const pos = document.positionAt(errorPosition);

            // 创建错误范围
            const range = new vscode.Range(pos, pos.translate(0, 1));

            // 检查常见的JSON错误
            if (errorMessage.includes('Expected') && errorMessage.includes('got')) {
              const expectedMatch = errorMessage.match(/Expected ([\s\S]+?), got/);
              const expected = expectedMatch ? expectedMatch[1] : '';

              let fixAction: FixAction;
              let beforePreview = '';
              let afterPreview = '';

              const lineText = document.lineAt(pos.line).text;

              if (errorMessage.includes('Expected property name')) {
                // 处理属性名错误
                fixAction = {
                  type: FixActionType.REPLACE,
                  start: range.start,
                  end: range.end,
                  text: '"propertyName":'
                };
                beforePreview = lineText;
                afterPreview = lineText.substring(0, pos.character) +
                              '"propertyName":' +
                              lineText.substring(pos.character + 1);
              } else if (errorMessage.includes('Expected ,')) {
                // 处理缺少逗号
                fixAction = {
                  type: FixActionType.INSERT,
                  start: range.start,
                  end: range.start,
                  text: ','
                };
                beforePreview = lineText;
                afterPreview = lineText.substring(0, pos.character) +
                              ',' +
                              lineText.substring(pos.character);
              } else {
                // 处理其他错误
                fixAction = {
                  type: FixActionType.REPLACE,
                  start: range.start,
                  end: range.end,
                  text: expected || '' // 确保text不为null
                };
                beforePreview = lineText;
                afterPreview = lineText.substring(0, pos.character) +
                              (expected || '') +
                              lineText.substring(pos.character + 1);
              }

              const fixSuggestion: FixSuggestion = {
                id: `fix-json-syntax-${Date.now()}`,
                title: `修复JSON语法: ${errorMessage}`,
                description: `应用建议的修复`,
                action: fixAction,
                beforePreview,
                afterPreview
              };

              const issue: DiagnosticIssue = {
                id: `issue-json-syntax-${Date.now()}`,
                message: `JSON语法错误: ${errorMessage}`,
                severity: DiagnosticSeverity.ERROR,
                source: DiagnosticSource.SYNTAX,
                range,
                file: document.uri,
                fixes: [fixSuggestion],
                context: lineText,
                timestamp: Date.now()
              };

              issues.push(issue);
            }
          }
        }
      }
    }

    return issues;
  }

  /**
   * 通过样式检查进行诊断
   * @param document 文档
   * @param content 文档内容
   * @param language 文档语言
   */
  private async diagnoseByStyle(
    document: vscode.TextDocument,
    content: string,
    language: string
  ): Promise<DiagnosticIssue[]> {
    const issues: DiagnosticIssue[] = [];

    // 根据不同语言实现不同的样式检查逻辑
    if (language === 'javascript' || language === 'typescript') {
      // 检查过长的行
      const styleLines = content.split('\n');
      const maxLineLength = 120; // 行长度上限

      for (let i = 0; i < styleLines.length; i++) {
        const line = styleLines[i];

        // 跳过注释和导入语句
        if (line.trim().startsWith('//') || line.trim().startsWith('import ')) {
          continue;
        }

        if (line.length > maxLineLength) {
          const range = new vscode.Range(
            new vscode.Position(i, maxLineLength),
            new vscode.Position(i, line.length)
          );

          // 尝试在适当位置拆分行
          let splitPos = maxLineLength;
          // 尝试在运算符处拆分
          const operators = [' + ', ' - ', ' * ', ' / ', ' && ', ' || ', ', ', ' => '];

          let bestSplitPos = -1;
          let bestSplitOp = '';

          for (const op of operators) {
            // 从maxLineLength向前查找最近的运算符
            let pos = line.lastIndexOf(op, maxLineLength);
            // 确保找到的位置不会过于靠前（避免拆分导致第一行过短）
            if (pos > maxLineLength * 0.7 && pos > bestSplitPos) {
              bestSplitPos = pos;
              bestSplitOp = op;
            }
          }

          if (bestSplitPos > 0) {
            splitPos = bestSplitPos + bestSplitOp.length;

            const indentation = line.match(/^\s*/)?.[0] || '';
            const fixSuggestion: FixSuggestion = {
              id: `fix-line-length-${Date.now()}`,
              title: '拆分长行',
              description: '将过长的行拆分为多行',
              action: {
                type: FixActionType.REPLACE,
                start: new vscode.Position(i, bestSplitPos),
                end: new vscode.Position(i, line.length),
                text: bestSplitOp + '\n' + indentation + '  ' + line.substring(splitPos).trim()
              },
              beforePreview: line,
              afterPreview: line.substring(0, bestSplitPos) + bestSplitOp +
                            '\n' + indentation + '  ' + line.substring(splitPos).trim()
            };

            const issue: DiagnosticIssue = {
              id: `issue-line-length-${Date.now()}`,
              message: `行长度超过${maxLineLength}个字符`,
              severity: DiagnosticSeverity.HINT,
              source: DiagnosticSource.STYLE,
              range,
              file: document.uri,
              fixes: [fixSuggestion],
              context: line,
              timestamp: Date.now()
            };

            issues.push(issue);
          }
        }
      }

      // 检查命名规范问题
      const varDeclarationRegex = /(const|let|var)\s+([A-Za-z0-9_$]+)\s*=/g;
      const functionDeclarationRegex = /function\s+([A-Za-z0-9_$]+)\s*\(/g;
      const camelCaseRegex = /^[a-z][a-zA-Z0-9]*$/;
      const pascalCaseRegex = /^[A-Z][a-zA-Z0-9]*$/;

      for (let i = 0; i < styleLines.length; i++) {
        const line = styleLines[i];

        // 检查变量命名
        let match;
        while ((match = varDeclarationRegex.exec(line)) !== null) {
          const varName = match[2];

          // 检查变量是否使用驼峰命名法（小写开头）
          if (!camelCaseRegex.test(varName) && !varName.includes('_')) {
            // 如果变量名是大写字母开头，可能是类或构造函数
            if (pascalCaseRegex.test(varName)) {
              continue;
            }

            const startPos = new vscode.Position(i, match.index + match[1].length + 1);
            const endPos = new vscode.Position(i, match.index + match[1].length + 1 + varName.length);
            const range = new vscode.Range(startPos, endPos);

            // 转换为驼峰命名法
            const camelCaseName = varName.replace(/_([a-z])/g, (_, char) => char.toUpperCase());

            const fixSuggestion: FixSuggestion = {
              id: `fix-naming-${Date.now()}`,
              title: `将变量名 "${varName}" 转换为驼峰命名法`,
              description: `改进变量命名风格`,
              action: {
                type: FixActionType.REPLACE,
                start: startPos,
                end: endPos,
                text: camelCaseName
              },
              beforePreview: varName,
              afterPreview: camelCaseName
            };

            const issue: DiagnosticIssue = {
              id: `issue-naming-${Date.now()}`,
              message: `变量 "${varName}" 未使用驼峰命名法`,
              severity: DiagnosticSeverity.HINT,
              source: DiagnosticSource.STYLE,
              range,
              file: document.uri,
              fixes: [fixSuggestion],
              context: line,
              timestamp: Date.now()
            };

            issues.push(issue);
          }
        }
      }
    } else if (language === 'python') {
      // 检查Python的PEP 8风格问题
      const pyStyleLines = content.split('\n');

      // 检查行长度
      const maxLineLength = 79; // PEP 8推荐的最大行长度

      for (let i = 0; i < pyStyleLines.length; i++) {
        const line = pyStyleLines[i];

        // 跳过注释和导入语句
        if (line.trim().startsWith('#') || line.trim().startsWith('import ')) {
          continue;
        }

        if (line.length > maxLineLength) {
          const range = new vscode.Range(
            new vscode.Position(i, maxLineLength),
            new vscode.Position(i, line.length)
          );

          const indentation = line.match(/^\s*/)?.[0] || '';
          const fixSuggestion: FixSuggestion = {
            id: `fix-python-line-length-${Date.now()}`,
            title: '按PEP 8拆分长行',
            description: '将过长的行拆分为多行',
            action: {
              type: FixActionType.REPLACE,
              start: new vscode.Position(i, maxLineLength - 10),
              end: new vscode.Position(i, line.length),
              text: ' \\\n' + indentation + '    ' + line.substring(maxLineLength - 10).trim()
            },
            beforePreview: line,
            afterPreview: line.substring(0, maxLineLength - 10) + ' \\\n' +
                          indentation + '    ' + line.substring(maxLineLength - 10).trim()
          };

          const issue: DiagnosticIssue = {
            id: `issue-python-line-length-${Date.now()}`,
            message: `行长度超过PEP 8推荐的${maxLineLength}个字符`,
            severity: DiagnosticSeverity.HINT,
            source: DiagnosticSource.STYLE,
            range,
            file: document.uri,
            fixes: [fixSuggestion],
            context: line,
            timestamp: Date.now()
          };

          issues.push(issue);
        }
      }
    }

    return issues;
  }

  /**
   * 获取问题列表
   * @param uri 文档URI
   */
  public getIssues(uri?: vscode.Uri): DiagnosticIssue[] {
    if (uri) {
      return this._issues.get(uri.toString()) || [];
    } else {
      // 返回所有问题
      const allIssues: DiagnosticIssue[] = [];
      this._issues.forEach(issues => {
        allIssues.push(...issues);
      });
      return allIssues;
    }
  }

  /**
   * 应用修复
   * @param issue 问题
   * @param fixIndex 修复索引
   */
  public async applyFix(issue: DiagnosticIssue, fixIndex: number = 0): Promise<boolean> {
    if (!issue.fixes || !issue.fixes[fixIndex]) {
      return false;
    }

    const fix = issue.fixes[fixIndex];
    const document = await vscode.workspace.openTextDocument(issue.file);

    // 创建编辑操作
    const edit = new vscode.WorkspaceEdit();

    // 根据不同操作类型执行不同编辑
    switch (fix.action.type) {
      case FixActionType.REPLACE:
        if (fix.action.text !== undefined) {
          const range = new vscode.Range(fix.action.start, fix.action.end);
          edit.replace(issue.file, range, fix.action.text);
        }
        break;

      case FixActionType.DELETE:
        const deleteRange = new vscode.Range(fix.action.start, fix.action.end);
        edit.delete(issue.file, deleteRange);
        break;

      case FixActionType.INSERT:
        if (fix.action.text !== undefined) {
          const position = fix.action.start;
          edit.insert(issue.file, position, fix.action.text);
        }
        break;

      case FixActionType.MULTIPLE:
        if (fix.action.actions) {
          // 处理多重操作
          for (const action of fix.action.actions) {
            await this.applyFixAction(edit, issue.file, action);
          }
        }
        break;
    }

    // 应用编辑
    return await vscode.workspace.applyEdit(edit);
  }

  /**
   * 应用修复操作
   * @param edit 工作区编辑
   * @param uri 文档URI
   * @param action 修复操作
   */
  private async applyFixAction(
    edit: vscode.WorkspaceEdit,
    uri: vscode.Uri,
    action: FixAction
  ): Promise<void> {
    switch (action.type) {
      case FixActionType.REPLACE:
        if (action.text !== undefined) {
          const range = new vscode.Range(action.start, action.end);
          edit.replace(uri, range, action.text);
        }
        break;

      case FixActionType.DELETE:
        const deleteRange = new vscode.Range(action.start, action.end);
        edit.delete(uri, deleteRange);
        break;

      case FixActionType.INSERT:
        if (action.text !== undefined) {
          const position = action.start;
          edit.insert(uri, position, action.text);
        }
        break;
    }
  }

  /**
   * 修复所有问题
   * @param uri 文档URI
   * @param options 修复选项
   */
  public async fixAllIssues(
    uri?: vscode.Uri,
    options: {
      severities?: DiagnosticSeverity[],
      sources?: DiagnosticSource[],
      fixOnlySelected?: boolean
    } = {}
  ): Promise<number> {
    let count = 0;

    // 获取问题列表
    let issues = this.getIssues(uri);

    // 根据选项过滤问题
    if (options.severities && options.severities.length > 0) {
      issues = issues.filter(issue => options.severities?.includes(issue.severity));
    }

    if (options.sources && options.sources.length > 0) {
      issues = issues.filter(issue => options.sources?.includes(issue.source));
    }

    // 如果选中了fixOnlySelected且当前有选中文本，则只修复选中范围内的问题
    let selectedRange: vscode.Range | undefined;
    if (options.fixOnlySelected && vscode.window.activeTextEditor) {
      selectedRange = vscode.window.activeTextEditor.selection;

      if (!selectedRange.isEmpty) {
        issues = issues.filter(issue => {
          return (
            issue.file.toString() === vscode.window.activeTextEditor?.document.uri.toString() &&
            (selectedRange?.contains(issue.range) || issue.range.intersection(selectedRange!))
          );
        });
      }
    }

    // 按严重性排序问题：错误 > 警告 > 提示 > 信息
    issues = issues.sort((a, b) => {
      const severityOrder = {
        [DiagnosticSeverity.ERROR]: 0,
        [DiagnosticSeverity.WARNING]: 1,
        [DiagnosticSeverity.HINT]: 2,
        [DiagnosticSeverity.INFO]: 3
      };

      return severityOrder[a.severity] - severityOrder[b.severity];
    });

    // 创建编辑操作
    const edit = new vscode.WorkspaceEdit();

    // 处理每个问题
    for (const issue of issues) {
      if (issue.fixes && issue.fixes.length > 0) {
        const fix = issue.fixes[0];

        // 尝试应用修复
        try {
          // 根据不同操作类型执行不同编辑
          switch (fix.action.type) {
            case FixActionType.REPLACE:
              if (fix.action.text !== undefined) {
                const range = new vscode.Range(fix.action.start, fix.action.end);
                edit.replace(issue.file, range, fix.action.text);
                count++;
              }
              break;

            case FixActionType.DELETE:
              const deleteRange = new vscode.Range(fix.action.start, fix.action.end);
              edit.delete(issue.file, deleteRange);
              count++;
              break;

            case FixActionType.INSERT:
              if (fix.action.text !== undefined) {
                const position = fix.action.start;
                edit.insert(issue.file, position, fix.action.text);
                count++;
              }
              break;

            case FixActionType.MULTIPLE:
              if (fix.action.actions) {
                // 处理多重操作
                for (const action of fix.action.actions) {
                  await this.applyFixAction(edit, issue.file, action);
                }
                count++;
              }
              break;
          }
        } catch (error) {
          console.error(`应用修复到问题 ${issue.id} 时出错:`, error);
          // 继续处理其他问题
        }
      }
    }

    // 应用编辑
    if (count > 0) {
      try {
        await vscode.workspace.applyEdit(edit);

        // 如果成功修复了问题，重新诊断文档
        if (uri && vscode.window.activeTextEditor?.document.uri.toString() === uri.toString()) {
          await this.diagnoseDocument(vscode.window.activeTextEditor.document);
        }

        return count;
      } catch (error) {
        console.error('应用批量修复时出错:', error);
        throw error;
      }
    }

    return count;
  }

  /**
   * 清除所有诊断
   */
  public clearDiagnostics(): void {
    this._diagnosticCollection.clear();
    this._issues.clear();
    this._onDiagnosticsChanged.fire([]);
  }

  /**
   * 映射规则严重性到诊断严重性
   * @param ruleSeverity 规则严重性
   */
  private mapRuleSeverity(ruleSeverity?: string): DiagnosticSeverity {
    switch (ruleSeverity) {
      case 'error':
        return DiagnosticSeverity.ERROR;
      case 'warning':
        return DiagnosticSeverity.WARNING;
      case 'hint':
        return DiagnosticSeverity.HINT;
      case 'info':
        return DiagnosticSeverity.INFO;
      default:
        return DiagnosticSeverity.WARNING;
    }
  }

  /**
   * 获取VSCode诊断严重性
   * @param severity 诊断严重性
   */
  private getSeverity(severity: DiagnosticSeverity): vscode.DiagnosticSeverity {
    switch (severity) {
      case DiagnosticSeverity.ERROR:
        return vscode.DiagnosticSeverity.Error;
      case DiagnosticSeverity.WARNING:
        return vscode.DiagnosticSeverity.Warning;
      case DiagnosticSeverity.HINT:
        return vscode.DiagnosticSeverity.Hint;
      case DiagnosticSeverity.INFO:
        return vscode.DiagnosticSeverity.Information;
      default:
        return vscode.DiagnosticSeverity.Warning;
    }
  }
}