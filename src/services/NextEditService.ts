import * as vscode from 'vscode';

/**
 * 编辑建议类型
 */
export interface EditSuggestion {
  id: string;
  title: string;
  description?: string;
  code: string;
  language: string;
  range?: vscode.Range; // 应用编辑的范围
}

/**
 * 编辑历史记录项
 */
export interface EditHistoryItem {
  id: string;
  suggestionId: string;
  timestamp: number;
  documentUri: string;
  range: vscode.Range;
  originalText: string;
  newText: string;
}

/**
 * 编辑建议服务类
 * 负责生成和管理编辑建议
 */
export class NextEditService {
  private static instance: NextEditService | undefined;
  private _suggestions: Map<string, EditSuggestion> = new Map();
  private _history: EditHistoryItem[] = [];
  private _onSuggestionsChanged = new vscode.EventEmitter<EditSuggestion[]>();
  private _onHistoryChanged = new vscode.EventEmitter<EditHistoryItem[]>();

  readonly onSuggestionsChanged = this._onSuggestionsChanged.event;
  readonly onHistoryChanged = this._onHistoryChanged.event;

  /**
   * 获取单例实例
   */
  public static getInstance(): NextEditService {
    if (!NextEditService.instance) {
      NextEditService.instance = new NextEditService();
    }
    return NextEditService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 为当前编辑器获取编辑建议
   * @returns 编辑建议数组
   */
  public async getSuggestions(): Promise<EditSuggestion[]> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return [];
    }

    // 清空旧的建议
    this._suggestions.clear();

    // 获取编辑器内容和语言
    const document = editor.document;
    const text = document.getText();
    const language = document.languageId;
    
    // 分析代码并生成建议
    const suggestions = await this.analyzeCodeAndGenerateSuggestions(text, language, editor);
    
    // 存储并返回建议
    suggestions.forEach(suggestion => {
      this._suggestions.set(suggestion.id, suggestion);
    });
    
    this._onSuggestionsChanged.fire(suggestions);
    return suggestions;
  }

  /**
   * 分析代码并生成编辑建议
   * 这是一个更智能的建议生成算法，基于代码分析
   */
  private async analyzeCodeAndGenerateSuggestions(
    text: string, 
    language: string, 
    editor: vscode.TextEditor
  ): Promise<EditSuggestion[]> {
    const suggestions: EditSuggestion[] = [];
    
    // 获取当前选择或光标位置
    const position = editor.selection.active;
    const selection = editor.selection;
    const lineText = editor.document.lineAt(position.line).text;
    const indentation = lineText.match(/^\s*/)?.[0] || '';
    
    // 分析代码上下文
    const codeContext = this.getCodeContext(text, position, language, editor);
    
    // 根据代码上下文生成建议
    if (language === 'javascript' || language === 'typescript') {
      this.generateJavaScriptSuggestions(suggestions, codeContext, indentation, position, language);
    } else if (language === 'html') {
      this.generateHtmlSuggestions(suggestions, codeContext, position, language);
    } else if (language === 'css') {
      this.generateCssSuggestions(suggestions, codeContext, position, language);
    } else if (language === 'json') {
      this.generateJsonSuggestions(suggestions, codeContext, indentation, position, language);
    } else {
      // 对于其他语言，添加一个通用注释
      suggestions.push({
        id: 'generic-' + Date.now(),
        title: '添加注释',
        description: '添加一个简单的注释',
        code: `${indentation}// 这是一个由LazyCode生成的注释`,
        language,
        range: new vscode.Range(position, position)
      });
    }
    
    return suggestions;
  }

  /**
   * 获取代码上下文信息
   */
  private getCodeContext(
    text: string, 
    position: vscode.Position, 
    language: string,
    editor: vscode.TextEditor
  ): any {
    // 获取光标前后的代码片段，用于分析上下文
    const linesBefore = this.getLinesBeforePosition(editor, position, 10);
    const linesAfter = this.getLinesAfterPosition(editor, position, 5);
    
    // 获取当前可见的代码范围
    const visibleRanges = editor.visibleRanges;
    const visibleText = visibleRanges.length > 0 
      ? editor.document.getText(visibleRanges[0]) 
      : '';
    
    // 检测变量名、函数名等
    const variables = this.extractVariables(linesBefore, language);
    const functions = this.extractFunctions(linesBefore, language);
    
    // 检测代码结构
    const isInsideFunction = this.isPositionInsideFunction(linesBefore, linesAfter, language);
    const isInsideClass = this.isPositionInsideClass(linesBefore, linesAfter, language);
    const isInsideLoop = this.isPositionInsideLoop(linesBefore, linesAfter, language);
    const isInsideCondition = this.isPositionInsideCondition(linesBefore, linesAfter, language);
    
    return {
      linesBefore,
      linesAfter,
      visibleText,
      variables,
      functions,
      isInsideFunction,
      isInsideClass,
      isInsideLoop,
      isInsideCondition
    };
  }

  /**
   * 获取位置前的代码行
   */
  private getLinesBeforePosition(
    editor: vscode.TextEditor, 
    position: vscode.Position, 
    lineCount: number
  ): string[] {
    const lines: string[] = [];
    const startLine = Math.max(0, position.line - lineCount);
    
    for (let i = startLine; i < position.line; i++) {
      lines.push(editor.document.lineAt(i).text);
    }
    
    // 添加当前行，但只有光标前的部分
    if (position.line < editor.document.lineCount) {
      const currentLine = editor.document.lineAt(position.line);
      lines.push(currentLine.text.substring(0, position.character));
    }
    
    return lines;
  }

  /**
   * 获取位置后的代码行
   */
  private getLinesAfterPosition(
    editor: vscode.TextEditor, 
    position: vscode.Position, 
    lineCount: number
  ): string[] {
    const lines: string[] = [];
    const endLine = Math.min(editor.document.lineCount - 1, position.line + lineCount);
    
    // 添加当前行，但只有光标后的部分
    if (position.line < editor.document.lineCount) {
      const currentLine = editor.document.lineAt(position.line);
      lines.push(currentLine.text.substring(position.character));
    }
    
    for (let i = position.line + 1; i <= endLine; i++) {
      lines.push(editor.document.lineAt(i).text);
    }
    
    return lines;
  }

  /**
   * 提取代码中的变量
   */
  private extractVariables(lines: string[], language: string): string[] {
    const variables: string[] = [];
    
    if (language === 'javascript' || language === 'typescript') {
      // 简单的正则匹配，在实际应用中可能需要更复杂的语法分析
      const varRegex = /(var|let|const)\s+(\w+)\s*=/g;
      let match;
      
      for (const line of lines) {
        while ((match = varRegex.exec(line)) !== null) {
          variables.push(match[2]);
        }
      }
    }
    
    return variables;
  }

  /**
   * 提取代码中的函数
   */
  private extractFunctions(lines: string[], language: string): string[] {
    const functions: string[] = [];
    
    if (language === 'javascript' || language === 'typescript') {
      // 简单的正则匹配，在实际应用中可能需要更复杂的语法分析
      const funcRegex = /function\s+(\w+)\s*\(/g;
      const methodRegex = /(\w+)\s*\([^)]*\)\s*{/g;
      let match;
      
      for (const line of lines) {
        while ((match = funcRegex.exec(line)) !== null) {
          functions.push(match[1]);
        }
        
        while ((match = methodRegex.exec(line)) !== null) {
          functions.push(match[1]);
        }
      }
    }
    
    return functions;
  }

  /**
   * A检查是否在函数内部
   */
  private isPositionInsideFunction(linesBefore: string[], linesAfter: string[], language: string): boolean {
    if (language !== 'javascript' && language !== 'typescript') {
      return false;
    }
    
    // 检查前面的代码是否有函数定义但没有闭合
    const openBraces = this.countOccurrences(linesBefore.join('\n'), '{');
    const closeBraces = this.countOccurrences(linesBefore.join('\n'), '}');
    
    if (openBraces <= closeBraces) {
      return false;
    }
    
    // 检查前面的代码是否包含函数定义
    const hasFuncDef = /function\s+\w+\s*\(|^[^{]*{/m.test(linesBefore.join('\n'));
    
    return hasFuncDef && (openBraces > closeBraces);
  }

  /**
   * 检查是否在类内部
   */
  private isPositionInsideClass(linesBefore: string[], linesAfter: string[], language: string): boolean {
    if (language !== 'javascript' && language !== 'typescript') {
      return false;
    }
    
    // 简单检查是否有类定义但没有闭合
    const hasClassDef = /class\s+\w+/.test(linesBefore.join('\n'));
    const openBraces = this.countOccurrences(linesBefore.join('\n'), '{');
    const closeBraces = this.countOccurrences(linesBefore.join('\n'), '}');
    
    return hasClassDef && (openBraces > closeBraces);
  }

  /**
   * 检查是否在循环内部
   */
  private isPositionInsideLoop(linesBefore: string[], linesAfter: string[], language: string): boolean {
    if (language !== 'javascript' && language !== 'typescript') {
      return false;
    }
    
    // 简单检查是否有循环定义
    const lastLines = linesBefore.slice(-3).join('\n');
    const hasLoopDef = /for\s*\(|while\s*\(/.test(lastLines);
    const openBraces = this.countOccurrences(lastLines, '{');
    const closeBraces = this.countOccurrences(lastLines, '}');
    
    return hasLoopDef && (openBraces > closeBraces);
  }

  /**
   * 检查是否在条件语句内部
   */
  private isPositionInsideCondition(linesBefore: string[], linesAfter: string[], language: string): boolean {
    if (language !== 'javascript' && language !== 'typescript') {
      return false;
    }
    
    // 简单检查是否有条件语句定义
    const lastLines = linesBefore.slice(-3).join('\n');
    const hasConditionDef = /if\s*\(|else\s*{|switch\s*\(/.test(lastLines);
    const openBraces = this.countOccurrences(lastLines, '{');
    const closeBraces = this.countOccurrences(lastLines, '}');
    
    return hasConditionDef && (openBraces > closeBraces);
  }

  /**
   * 计算字符串中指定字符出现的次数
   */
  private countOccurrences(str: string, char: string): number {
    return (str.match(new RegExp(char, 'g')) || []).length;
  }

  /**
   * 为JavaScript/TypeScript生成建议
   */
  private generateJavaScriptSuggestions(
    suggestions: EditSuggestion[], 
    context: any, 
    indentation: string, 
    position: vscode.Position,
    language: string
  ): void {
    // 检查代码上下文并生成相应建议
    if (context.isInsideFunction) {
      // 在函数内部添加常用代码片段
      suggestions.push({
        id: 'js-return-' + Date.now(),
        title: '添加返回语句',
        description: '添加函数返回语句',
        code: `${indentation}return result;`,
        language,
        range: new vscode.Range(position, position)
      });
      
      if (context.variables.length > 0) {
        // 如果有变量，提供操作变量的建议
        const varName = context.variables[context.variables.length - 1];
        suggestions.push({
          id: 'js-var-log-' + Date.now(),
          title: `记录变量 ${varName}`,
          description: `添加console.log语句记录变量${varName}的值`,
          code: `${indentation}console.log('${varName}:', ${varName});`,
          language,
          range: new vscode.Range(position, position)
        });
      }
      
      // 添加错误处理建议
      suggestions.push({
        id: 'js-try-catch-' + Date.now(),
        title: '添加错误处理',
        description: '使用try-catch包装代码块',
        code: `${indentation}try {
${indentation}  // 可能出错的代码
${indentation}} catch (error) {
${indentation}  console.error('发生错误:', error);
${indentation}}`,
        language,
        range: new vscode.Range(position, position)
      });
    } else if (context.isInsideClass) {
      // 在类内部添加方法
      suggestions.push({
        id: 'js-method-' + Date.now(),
        title: '添加类方法',
        description: '添加一个新的类方法',
        code: `${indentation}/**
${indentation} * 新方法描述
${indentation} * @param {any} param 参数描述
${indentation} * @returns {any} 返回值描述
${indentation} */
${indentation}methodName(param) {
${indentation}  // 方法实现
${indentation}  return param;
${indentation}}`,
        language,
        range: new vscode.Range(position, position)
      });
      
      // 添加getter和setter
      suggestions.push({
        id: 'js-getter-setter-' + Date.now(),
        title: '添加Getter和Setter',
        description: '为类属性添加getter和setter方法',
        code: `${indentation}get propertyName() {
${indentation}  return this._propertyName;
${indentation}}

${indentation}set propertyName(value) {
${indentation}  this._propertyName = value;
${indentation}}`,
        language,
        range: new vscode.Range(position, position)
      });
    } else {
      // 添加函数定义
      suggestions.push({
        id: 'js-function-' + Date.now(),
        title: '添加函数',
        description: '添加一个新的函数定义',
        code: `${indentation}/**
${indentation} * 计算两个数的和
${indentation} * @param {number} a 第一个数
${indentation} * @param {number} b 第二个数
${indentation} * @returns {number} 两数之和
${indentation} */
${indentation}function sum(a, b) {
${indentation}  return a + b;
${indentation}}`,
        language,
        range: new vscode.Range(position, position)
      });
      
      // 添加类定义
      suggestions.push({
        id: 'js-class-' + Date.now(),
        title: '添加类',
        description: '添加一个新的类定义',
        code: `${indentation}/**
${indentation} * 数据模型类
${indentation} */
${indentation}class DataModel {
${indentation}  /**
${indentation}   * 构造函数
${indentation}   * @param {any} data 初始数据
${indentation}   */
${indentation}  constructor(data) {
${indentation}    this._data = data;
${indentation}  }

${indentation}  /**
${indentation}   * 获取数据
${indentation}   * @returns {any} 存储的数据
${indentation}   */
${indentation}  getData() {
${indentation}    return this._data;
${indentation}  }

${indentation}  /**
${indentation}   * 设置数据
${indentation}   * @param {any} data 新数据
${indentation}   */
${indentation}  setData(data) {
${indentation}    this._data = data;
${indentation}  }
${indentation}}`,
        language,
        range: new vscode.Range(position, position)
      });
      
      // 添加注释建议
      suggestions.push({
        id: 'js-comment-' + Date.now(),
        title: '添加TODO注释',
        description: '添加一个TODO注释',
        code: `${indentation}// TODO: 这里需要实现更多功能`,
        language,
        range: new vscode.Range(position, position)
      });
    }
  }

  /**
   * 为HTML生成建议
   */
  private generateHtmlSuggestions(
    suggestions: EditSuggestion[], 
    context: any, 
    position: vscode.Position,
    language: string
  ): void {
    // 添加div容器
    suggestions.push({
      id: 'html-div-' + Date.now(),
      title: '添加容器',
      description: '添加一个带类的div容器',
      code: '<div class="container">\n  <h1>标题</h1>\n  <p>内容</p>\n</div>',
      language,
      range: new vscode.Range(position, position)
    });
    
    // 添加表单
    suggestions.push({
      id: 'html-form-' + Date.now(),
      title: '添加表单',
      description: '添加一个输入表单',
      code: '<form class="form">\n  <div class="form-group">\n    <label for="username">用户名:</label>\n    <input type="text" id="username" name="username" required>\n  </div>\n  <div class="form-group">\n    <label for="password">密码:</label>\n    <input type="password" id="password" name="password" required>\n  </div>\n  <button type="submit" class="btn btn-primary">提交</button>\n</form>',
      language,
      range: new vscode.Range(position, position)
    });
    
    // 添加导航菜单
    suggestions.push({
      id: 'html-nav-' + Date.now(),
      title: '添加导航',
      description: '添加一个导航菜单',
      code: '<nav class="navbar">\n  <ul class="nav-list">\n    <li class="nav-item"><a href="/">首页</a></li>\n    <li class="nav-item"><a href="/about">关于</a></li>\n    <li class="nav-item"><a href="/services">服务</a></li>\n    <li class="nav-item"><a href="/contact">联系我们</a></li>\n  </ul>\n</nav>',
      language,
      range: new vscode.Range(position, position)
    });
    
    // 添加卡片组件
    suggestions.push({
      id: 'html-card-' + Date.now(),
      title: '添加卡片',
      description: '添加一个卡片组件',
      code: '<div class="card">\n  <div class="card-header">\n    <h3>卡片标题</h3>\n  </div>\n  <div class="card-body">\n    <p>卡片内容</p>\n  </div>\n  <div class="card-footer">\n    <button class="btn">详情</button>\n  </div>\n</div>',
      language,
      range: new vscode.Range(position, position)
    });
  }

  /**
   * 为CSS生成建议
   */
  private generateCssSuggestions(
    suggestions: EditSuggestion[], 
    context: any, 
    position: vscode.Position,
    language: string
  ): void {
    // 添加容器样式
    suggestions.push({
      id: 'css-container-' + Date.now(),
      title: '添加容器样式',
      description: '为容器添加基本样式',
      code: '.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 15px;\n  box-sizing: border-box;\n}',
      language,
      range: new vscode.Range(position, position)
    });
    
    // 添加弹性布局
    suggestions.push({
      id: 'css-flex-' + Date.now(),
      title: '添加弹性布局',
      description: '创建弹性布局样式',
      code: '.flex-container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n}',
      language,
      range: new vscode.Range(position, position)
    });
    
    // 添加响应式媒体查询
    suggestions.push({
      id: 'css-media-query-' + Date.now(),
      title: '添加媒体查询',
      description: '添加响应式设计媒体查询',
      code: '/* 移动设备 */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 10px;\n  }\n}\n\n/* 平板设备 */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .container {\n    padding: 0 20px;\n  }\n}\n\n/* 桌面设备 */\n@media (min-width: 1025px) {\n  .container {\n    padding: 0 30px;\n  }\n}',
      language,
      range: new vscode.Range(position, position)
    });
    
    // 添加按钮样式
    suggestions.push({
      id: 'css-button-' + Date.now(),
      title: '添加按钮样式',
      description: '创建样式化按钮',
      code: '.button {\n  display: inline-block;\n  padding: 10px 20px;\n  background-color: #4CAF50;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.button:hover {\n  background-color: #45a049;\n}\n\n.button:active {\n  background-color: #3e8e41;\n}\n\n.button-secondary {\n  background-color: #2196F3;\n}\n\n.button-secondary:hover {\n  background-color: #0b7dda;\n}',
      language,
      range: new vscode.Range(position, position)
    });
  }

  /**
   * 为JSON生成建议
   */
  private generateJsonSuggestions(
    suggestions: EditSuggestion[], 
    context: any, 
    indentation: string, 
    position: vscode.Position,
    language: string
  ): void {
    // 添加配置对象
    suggestions.push({
      id: 'json-config-' + Date.now(),
      title: '添加配置对象',
      description: '添加一个配置对象',
      code: `${indentation}{\n${indentation}  "name": "example",\n${indentation}  "version": "1.0.0",\n${indentation}  "description": "示例配置",\n${indentation}  "options": {\n${indentation}    "enabled": true,\n${indentation}    "timeout": 5000,\n${indentation}    "retries": 3\n${indentation}  }\n${indentation}}`,
      language,
      range: new vscode.Range(position, position)
    });
    
    // 添加数组
    suggestions.push({
      id: 'json-array-' + Date.now(),
      title: '添加数组',
      description: '添加一个项目数组',
      code: `${indentation}[\n${indentation}  {\n${indentation}    "id": 1,\n${indentation}    "name": "项目一",\n${indentation}    "active": true\n${indentation}  },\n${indentation}  {\n${indentation}    "id": 2,\n${indentation}    "name": "项目二",\n${indentation}    "active": false\n${indentation}  },\n${indentation}  {\n${indentation}    "id": 3,\n${indentation}    "name": "项目三",\n${indentation}    "active": true\n${indentation}  }\n${indentation}]`,
      language,
      range: new vscode.Range(position, position)
    });
  }

  /**
   * 应用编辑建议
   * @param suggestionId 建议ID
   */
  public async applySuggestion(suggestionId: string): Promise<boolean> {
    const suggestion = this._suggestions.get(suggestionId);
    if (!suggestion) {
      return false;
    }

    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return false;
    }

    // 确定编辑范围
    const range = suggestion.range || new vscode.Range(
      new vscode.Position(0, 0),
      new vscode.Position(editor.document.lineCount, 0)
    );

    // 保存原始文本，用于记录历史
    const originalText = editor.document.getText(range);

    // 应用编辑
    const result = await editor.edit(editBuilder => {
      editBuilder.replace(range, suggestion.code);
    });

    // 如果成功应用，记录到历史并发送通知
    if (result) {
      this.addToHistory({
        id: 'history-' + Date.now(),
        suggestionId: suggestionId,
        timestamp: Date.now(),
        documentUri: editor.document.uri.toString(),
        range: range,
        originalText: originalText,
        newText: suggestion.code
      });
      
      vscode.window.showInformationMessage(`已应用建议: ${suggestion.title}`);
    }

    return result;
  }

  /**
   * 添加编辑记录到历史
   */
  private addToHistory(historyItem: EditHistoryItem): void {
    // 将新的历史项添加到开头
    this._history.unshift(historyItem);
    
    // 保持历史记录不超过20项
    if (this._history.length > 20) {
      this._history = this._history.slice(0, 20);
    }
    
    // 通知历史变化
    this._onHistoryChanged.fire(this._history);
  }

  /**
   * 获取编辑历史
   */
  public getHistory(): EditHistoryItem[] {
    return [...this._history];
  }

  /**
   * 撤销最后的编辑
   */
  public async undoLastEdit(): Promise<boolean> {
    if (this._history.length === 0) {
      vscode.window.showInformationMessage('没有可撤销的编辑历史');
      return false;
    }
    
    const lastEdit = this._history[0];
    const editor = vscode.window.activeTextEditor;
    
    // 检查是否是当前文档
    if (!editor || editor.document.uri.toString() !== lastEdit.documentUri) {
      vscode.window.showErrorMessage('无法撤销：编辑发生在不同的文档中');
      return false;
    }
    
    // 撤销编辑
    const result = await editor.edit(editBuilder => {
      editBuilder.replace(lastEdit.range, lastEdit.originalText);
    });
    
    // 如果成功撤销，移除历史项
    if (result) {
      this._history.shift();
      this._onHistoryChanged.fire(this._history);
      vscode.window.showInformationMessage('已撤销上一次编辑');
    }
    
    return result;
  }

  /**
   * 获取指定ID的建议
   * @param suggestionId 建议ID
   */
  public getSuggestionById(suggestionId: string): EditSuggestion | undefined {
    return this._suggestions.get(suggestionId);
  }

  /**
   * 获取当前所有建议
   */
  public getAllSuggestions(): EditSuggestion[] {
    return Array.from(this._suggestions.values());
  }
} 