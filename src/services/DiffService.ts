import * as vscode from 'vscode';
import * as diff from 'diff';

/**
 * 差异比较模式枚举
 */
export enum DiffMode {
  /**
   * 按行比较
   */
  LINE = 'line',
  
  /**
   * 按单词比较
   */
  WORD = 'word',
  
  /**
   * 按字符比较
   */
  CHARACTER = 'character'
}

/**
 * 差异类型枚举
 */
export enum DiffType {
  /**
   * 插入的内容
   */
  ADDED = 'added',
  
  /**
   * 删除的内容
   */
  REMOVED = 'removed',
  
  /**
   * 未改变的内容
   */
  UNCHANGED = 'unchanged'
}

/**
 * 差异片段接口
 */
export interface DiffChunk {
  /**
   * 差异类型
   */
  type: DiffType;
  
  /**
   * 差异内容
   */
  content: string;
  
  /**
   * 行号信息（可选）
   */
  lineInfo?: {
    oldStart?: number;
    oldEnd?: number;
    newStart?: number;
    newEnd?: number;
  };
}

/**
 * 差异比较结果接口
 */
export interface DiffResult {
  /**
   * 差异片段列表
   */
  chunks: DiffChunk[];
  
  /**
   * 差异统计信息
   */
  stats: {
    /**
     * 添加的行数
     */
    added: number;
    
    /**
     * 删除的行数
     */
    removed: number;
    
    /**
     * 未变化的行数
     */
    unchanged: number;
  };
  
  /**
   * 原始文本
   */
  oldText: string;
  
  /**
   * 新文本
   */
  newText: string;
  
  /**
   * 比较模式
   */
  mode: DiffMode;
  
  /**
   * 生成时间
   */
  timestamp: number;
}

/**
 * 差异比较服务类
 * 负责生成和管理代码差异比较
 */
export class DiffService {
  private static instance: DiffService | undefined;
  private _onDiffGenerated = new vscode.EventEmitter<DiffResult>();
  
  readonly onDiffGenerated = this._onDiffGenerated.event;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): DiffService {
    if (!DiffService.instance) {
      DiffService.instance = new DiffService();
    }
    return DiffService.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {}
  
  /**
   * 初始化差异比较服务
   * @param context 扩展上下文
   */
  public initialize(context: vscode.ExtensionContext): void {
    // 注册命令
    context.subscriptions.push(
      vscode.commands.registerCommand('lazycode.compareWithClipboard', () => this.compareWithClipboard())
    );
    
    context.subscriptions.push(
      vscode.commands.registerCommand('lazycode.compareWithPrevious', () => this.compareWithPrevious())
    );
    
    context.subscriptions.push(
      vscode.commands.registerCommand('lazycode.compareFiles', () => this.compareFiles())
    );
  }
  
  /**
   * 比较两段文本
   * @param oldText 旧文本
   * @param newText 新文本
   * @param mode 比较模式
   * @returns 差异比较结果
   */
  public compareText(oldText: string, newText: string, mode: DiffMode = DiffMode.LINE): DiffResult {
    let chunks: DiffChunk[] = [];
    let stats = { added: 0, removed: 0, unchanged: 0 };
    
    try {
      // 根据模式选择不同的比较方法
      let diffResult;
      
      switch (mode) {
        case DiffMode.LINE:
          diffResult = diff.diffLines(oldText, newText);
          break;
        case DiffMode.WORD:
          diffResult = diff.diffWords(oldText, newText);
          break;
        case DiffMode.CHARACTER:
          diffResult = diff.diffChars(oldText, newText);
          break;
        default:
          diffResult = diff.diffLines(oldText, newText);
      }
      
      // 计算行号
      let oldLineNumber = 1;
      let newLineNumber = 1;
      
      // 处理比较结果
      chunks = diffResult.map(part => {
        const chunk: DiffChunk = {
          type: part.added ? DiffType.ADDED : (part.removed ? DiffType.REMOVED : DiffType.UNCHANGED),
          content: part.value,
          lineInfo: {}
        };
        
        // 计算行号信息
        if (mode === DiffMode.LINE) {
          const lineCount = (part.value.match(/\n/g) || []).length + (part.value.endsWith('\n') ? 0 : 1);
          
          if (part.added) {
            chunk.lineInfo = {
              newStart: newLineNumber,
              newEnd: newLineNumber + lineCount - 1
            };
            newLineNumber += lineCount;
            stats.added += lineCount;
          } else if (part.removed) {
            chunk.lineInfo = {
              oldStart: oldLineNumber,
              oldEnd: oldLineNumber + lineCount - 1
            };
            oldLineNumber += lineCount;
            stats.removed += lineCount;
          } else {
            chunk.lineInfo = {
              oldStart: oldLineNumber,
              oldEnd: oldLineNumber + lineCount - 1,
              newStart: newLineNumber,
              newEnd: newLineNumber + lineCount - 1
            };
            oldLineNumber += lineCount;
            newLineNumber += lineCount;
            stats.unchanged += lineCount;
          }
        } else {
          // 对于单词和字符级别的比较，统计添加和删除的数量
          if (part.added) {
            stats.added += part.value.length;
          } else if (part.removed) {
            stats.removed += part.value.length;
          } else {
            stats.unchanged += part.value.length;
          }
        }
        
        return chunk;
      });
      
      // 生成差异结果
      const result: DiffResult = {
        chunks,
        stats,
        oldText,
        newText,
        mode,
        timestamp: Date.now()
      };
      
      // 触发事件
      this._onDiffGenerated.fire(result);
      
      return result;
    } catch (error) {
      console.error('生成差异比较时出错:', error);
      
      // 返回空结果
      return {
        chunks: [],
        stats: { added: 0, removed: 0, unchanged: 0 },
        oldText,
        newText,
        mode,
        timestamp: Date.now()
      };
    }
  }
  
  /**
   * 与剪贴板内容比较
   */
  public async compareWithClipboard(): Promise<DiffResult | undefined> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      vscode.window.showErrorMessage('没有活动的编辑器');
      return;
    }
    
    try {
      // 获取剪贴板内容
      const clipboardText = await vscode.env.clipboard.readText();
      
      if (!clipboardText) {
        vscode.window.showInformationMessage('剪贴板中没有内容');
        return;
      }
      
      // 获取当前编辑器内容
      const editorText = editor.document.getText();
      
      // 比较内容
      const result = this.compareText(clipboardText, editorText);
      
      // 返回比较结果
      return result;
    } catch (error) {
      vscode.window.showErrorMessage(`与剪贴板比较失败: ${error instanceof Error ? error.message : String(error)}`);
      return;
    }
  }
  
  /**
   * 与上一版本比较
   */
  public async compareWithPrevious(): Promise<DiffResult | undefined> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      vscode.window.showErrorMessage('没有活动的编辑器');
      return;
    }
    
    try {
      // 获取文件URI
      const uri = editor.document.uri;
      
      // 检查文件是否在版本控制中
      const gitExtension = vscode.extensions.getExtension('vscode.git');
      if (!gitExtension) {
        vscode.window.showInformationMessage('未找到Git扩展，无法获取上一版本');
        return;
      }
      
      // 激活Git扩展
      const git = await gitExtension.activate();
      if (!git) {
        vscode.window.showInformationMessage('无法激活Git扩展');
        return;
      }
      
      // 获取当前仓库
      const api = git.getAPI(1);
      const repositories = api.repositories;
      
      if (!repositories || repositories.length === 0) {
        vscode.window.showInformationMessage('未找到Git仓库');
        return;
      }
      
      // 找到包含当前文件的仓库
      let repository;
      for (const repo of repositories) {
        if (uri.fsPath.startsWith(repo.rootUri.fsPath)) {
          repository = repo;
          break;
        }
      }
      
      if (!repository) {
        vscode.window.showInformationMessage('当前文件不在Git仓库中');
        return;
      }
      
      // 获取当前内容
      const currentContent = editor.document.getText();
      
      // 获取上一版本内容
      const relativePath = vscode.workspace.asRelativePath(uri);
      
      // 执行git命令获取上一版本内容
      const previousContent = await new Promise<string>((resolve, reject) => {
        const cp = require('child_process');
        cp.exec(`git show HEAD:${relativePath}`, { cwd: repository.rootUri.fsPath }, (err: any, stdout: string, stderr: string) => {
          if (err) {
            reject(new Error(`执行Git命令失败: ${stderr}`));
            return;
          }
          resolve(stdout);
        });
      });
      
      // 比较内容
      const result = this.compareText(previousContent, currentContent);
      
      // 返回比较结果
      return result;
    } catch (error) {
      vscode.window.showErrorMessage(`与上一版本比较失败: ${error instanceof Error ? error.message : String(error)}`);
      return;
    }
  }
  
  /**
   * 比较两个文件
   */
  public async compareFiles(): Promise<DiffResult | undefined> {
    try {
      // 让用户选择第一个文件
      const file1Uri = await vscode.window.showOpenDialog({
        canSelectFiles: true,
        canSelectFolders: false,
        canSelectMany: false,
        openLabel: '选择第一个文件'
      });
      
      if (!file1Uri || file1Uri.length === 0) {
        return;
      }
      
      // 让用户选择第二个文件
      const file2Uri = await vscode.window.showOpenDialog({
        canSelectFiles: true,
        canSelectFolders: false,
        canSelectMany: false,
        openLabel: '选择第二个文件'
      });
      
      if (!file2Uri || file2Uri.length === 0) {
        return;
      }
      
      // 读取文件内容
      const file1Content = await vscode.workspace.fs.readFile(file1Uri[0]);
      const file2Content = await vscode.workspace.fs.readFile(file2Uri[0]);
      
      // 转换为字符串
      const file1Text = Buffer.from(file1Content).toString('utf8');
      const file2Text = Buffer.from(file2Content).toString('utf8');
      
      // 比较内容
      const result = this.compareText(file1Text, file2Text);
      
      // 返回比较结果
      return result;
    } catch (error) {
      vscode.window.showErrorMessage(`比较文件失败: ${error instanceof Error ? error.message : String(error)}`);
      return;
    }
  }
} 