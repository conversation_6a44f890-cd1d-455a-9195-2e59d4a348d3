import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 规则类型枚举
 */
export enum RuleType {
  /**
   * 代码生成规则
   */
  GENERATION = 'generation',
  
  /**
   * 代码分析规则
   */
  ANALYSIS = 'analysis',
  
  /**
   * 代码转换规则
   */
  TRANSFORMATION = 'transformation',
  
  /**
   * 自定义规则
   */
  CUSTOM = 'custom'
}

/**
 * 规则对象接口
 */
export interface Rule {
  /**
   * 规则ID
   */
  id: string;
  
  /**
   * 规则名称
   */
  name: string;
  
  /**
   * 规则描述
   */
  description: string;
  
  /**
   * 规则类型
   */
  type: RuleType;
  
  /**
   * 规则内容
   */
  content: string;
  
  /**
   * 是否启用
   */
  enabled: boolean;
  
  /**
   * 应用顺序
   */
  order: number;
  
  /**
   * 创建时间
   */
  createdAt: number;
  
  /**
   * 更新时间
   */
  updatedAt: number;
  
  /**
   * 适用的语言
   */
  languages: string[];
  
  /**
   * 标签
   */
  tags: string[];
  
  /**
   * 匹配模式（正则表达式）
   */
  pattern?: string;
  
  /**
   * 替换文本
   */
  replacement?: string;
  
  /**
   * 规则严重性
   */
  severity?: string;
}

/**
 * 规则服务类
 * 负责规则的加载、保存和应用
 */
export class RulesService {
  private static instance: RulesService | undefined;
  private _rules: Map<string, Rule> = new Map();
  private _rulesDirectory: string = '';
  private _onRulesChanged = new vscode.EventEmitter<Rule[]>();
  
  readonly onRulesChanged = this._onRulesChanged.event;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): RulesService {
    if (!RulesService.instance) {
      RulesService.instance = new RulesService();
    }
    return RulesService.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    // 获取规则目录路径
    this._rulesDirectory = path.join(
      vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '',
      '.cursor',
      'rules'
    );
  }
  
  /**
   * 初始化规则服务
   * @param context 扩展上下文
   */
  public async initialize(context: vscode.ExtensionContext): Promise<void> {
    // 确保规则目录存在
    await this.ensureRulesDirectory();
    
    // 加载规则
    await this.loadRules();
    
    // 注册规则文件变化监听
    const rulesWatcher = vscode.workspace.createFileSystemWatcher(
      new vscode.RelativePattern(this._rulesDirectory, '*.{md,mdc}')
    );
    
    rulesWatcher.onDidChange(() => this.loadRules());
    rulesWatcher.onDidCreate(() => this.loadRules());
    rulesWatcher.onDidDelete(() => this.loadRules());
    
    context.subscriptions.push(rulesWatcher);
  }
  
  /**
   * 确保规则目录存在
   */
  private async ensureRulesDirectory(): Promise<void> {
    try {
      // 检查规则目录是否存在，不存在则创建
      if (!fs.existsSync(this._rulesDirectory)) {
        fs.mkdirSync(this._rulesDirectory, { recursive: true });
        console.log(`已创建规则目录: ${this._rulesDirectory}`);
      }
    } catch (error) {
      console.error('创建规则目录失败:', error);
      throw error;
    }
  }
  
  /**
   * 加载所有规则
   */
  public async loadRules(): Promise<Rule[]> {
    try {
      // 清空当前规则
      this._rules.clear();
      
      // 获取规则文件列表
      const files = fs.readdirSync(this._rulesDirectory);
      const ruleFiles = files.filter(file => file.endsWith('.md') || file.endsWith('.mdc'));
      
      // 处理每个规则文件
      for (const file of ruleFiles) {
        const filePath = path.join(this._rulesDirectory, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 解析文件名作为规则ID
        const id = path.basename(file, path.extname(file));
        
        // 解析规则头部信息
        const rule = this.parseRuleContent(id, content);
        
        if (rule) {
          this._rules.set(rule.id, rule);
        }
      }
      
      // 触发规则变化事件
      const rules = Array.from(this._rules.values());
      this._onRulesChanged.fire(rules);
      
      return rules;
    } catch (error) {
      console.error('加载规则失败:', error);
      return [];
    }
  }
  
  /**
   * 解析规则内容
   * @param id 规则ID
   * @param content 规则内容
   */
  private parseRuleContent(id: string, content: string): Rule | null {
    try {
      // 提取规则元数据
      const nameMatch = content.match(/^# (.+)/m);
      const name = nameMatch ? nameMatch[1] : id;
      
      const descriptionMatch = content.match(/^## (.+)/m);
      const description = descriptionMatch ? descriptionMatch[1] : '';
      
      // 默认规则类型为自定义
      const type = RuleType.CUSTOM;
      
      // 提取规则适用语言和标签（如果有）
      const languages: string[] = [];
      const tags: string[] = [];
      
      // 提取匹配模式（如果有）
      const patternMatch = content.match(/Pattern:\s*\/(.+)\/([gimuy]*)/m);
      const pattern = patternMatch ? patternMatch[0] : undefined;
      
      // 提取替换文本（如果有）
      const replacementMatch = content.match(/Replacement:\s*`?([^`]*)`?/m);
      const replacement = replacementMatch ? replacementMatch[1] : undefined;
      
      // 提取规则严重性（如果有）
      const severityMatch = content.match(/Severity:\s*(error|warning|hint|info)/i);
      const severity = severityMatch ? severityMatch[1].toLowerCase() : undefined;
      
      // 创建规则对象
      const rule: Rule = {
        id,
        name,
        description,
        type,
        content,
        enabled: true,
        order: 0,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        languages,
        tags,
        pattern,
        replacement,
        severity
      };
      
      return rule;
    } catch (error) {
      console.error(`解析规则 ${id} 失败:`, error);
      return null;
    }
  }
  
  /**
   * 获取所有规则
   */
  public getAllRules(): Rule[] {
    return Array.from(this._rules.values());
  }
  
  /**
   * 获取指定ID的规则
   * @param id 规则ID
   */
  public getRuleById(id: string): Rule | undefined {
    return this._rules.get(id);
  }
  
  /**
   * 根据语言获取规则
   * @param language 语言ID
   * @returns 适用于该语言的规则列表
   */
  public getRulesByLanguage(language: string): Rule[] {
    const rules: Rule[] = [];
    
    this._rules.forEach(rule => {
      // 如果规则适用于所有语言或特定语言
      if (rule.languages.length === 0 || rule.languages.includes(language)) {
        rules.push(rule);
      }
    });
    
    return rules;
  }
  
  /**
   * 根据类型获取规则
   * @param type 规则类型
   */
  public getRulesByType(type: RuleType): Rule[] {
    return Array.from(this._rules.values()).filter(rule => rule.type === type);
  }
  
  /**
   * 创建新规则
   * @param rule 规则对象
   */
  public async createRule(rule: Omit<Rule, 'id' | 'createdAt' | 'updatedAt'>): Promise<Rule> {
    // 生成规则ID
    const id = `rule-${Date.now()}`;
    
    // 创建完整规则对象
    const newRule: Rule = {
      ...rule,
      id,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    
    // 保存规则文件
    await this.saveRuleToFile(newRule);
    
    // 添加到规则集合
    this._rules.set(id, newRule);
    
    // 触发规则变化事件
    this._onRulesChanged.fire(this.getAllRules());
    
    return newRule;
  }
  
  /**
   * 更新规则
   * @param id 规则ID
   * @param rule 更新的规则内容
   */
  public async updateRule(id: string, rule: Partial<Rule>): Promise<Rule | undefined> {
    // 获取原规则
    const existingRule = this._rules.get(id);
    if (!existingRule) {
      return undefined;
    }
    
    // 创建更新后的规则对象
    const updatedRule: Rule = {
      ...existingRule,
      ...rule,
      updatedAt: Date.now()
    };
    
    // 保存规则文件
    await this.saveRuleToFile(updatedRule);
    
    // 更新规则集合
    this._rules.set(id, updatedRule);
    
    // 触发规则变化事件
    this._onRulesChanged.fire(this.getAllRules());
    
    return updatedRule;
  }
  
  /**
   * 删除规则
   * @param id 规则ID
   */
  public async deleteRule(id: string): Promise<boolean> {
    // 获取规则
    const rule = this._rules.get(id);
    if (!rule) {
      return false;
    }
    
    // 删除规则文件
    const filePath = path.join(this._rulesDirectory, `${id}.md`);
    try {
      fs.unlinkSync(filePath);
    } catch (error) {
      console.error(`删除规则文件失败: ${filePath}`, error);
      return false;
    }
    
    // 从规则集合中移除
    this._rules.delete(id);
    
    // 触发规则变化事件
    this._onRulesChanged.fire(this.getAllRules());
    
    return true;
  }
  
  /**
   * 保存规则到文件
   * @param rule 规则对象
   */
  private async saveRuleToFile(rule: Rule): Promise<void> {
    // 构建规则内容
    const content = [
      `# ${rule.name}`,
      `## ${rule.description}`,
      ``,
      `Type: ${rule.type}`,
      `Enabled: ${rule.enabled}`,
      `Languages: ${rule.languages.join(', ')}`,
      `Tags: ${rule.tags.join(', ')}`,
      ``,
      rule.content
    ].join('\n');
    
    // 保存到文件
    const filePath = path.join(this._rulesDirectory, `${rule.id}.md`);
    fs.writeFileSync(filePath, content, 'utf8');
  }
  
  /**
   * 应用规则到指定内容
   * @param content 要处理的内容
   * @param language 语言
   * @param ruleType 规则类型
   */
  public applyRules(content: string, language: string, ruleType: RuleType): string {
    // 获取对应类型和语言的规则
    const rules = this.getAllRules()
      .filter(rule => rule.enabled)
      .filter(rule => rule.type === ruleType)
      .filter(rule => rule.languages.length === 0 || rule.languages.includes(language))
      .sort((a, b) => a.order - b.order);
    
    // 依次应用规则
    let result = content;
    for (const rule of rules) {
      try {
        // 简单应用规则，实际应用中可能需要更复杂的处理
        result = this.processSingleRule(result, rule);
      } catch (error) {
        console.error(`应用规则 ${rule.id} 失败:`, error);
      }
    }
    
    return result;
  }
  
  /**
   * 处理单条规则
   * @param content 内容
   * @param rule 规则
   */
  private processSingleRule(content: string, rule: Rule): string {
    try {
      // 从规则内容中提取规则处理逻辑
      const ruleContent = this.parseRuleContent(rule.id, rule.content);
      if (!ruleContent) {
        return content;
      }
      
      // 根据规则类型应用不同的处理逻辑
      switch (rule.type) {
        case RuleType.TRANSFORMATION:
          return this.applyTransformationRule(content, rule);
        case RuleType.ANALYSIS:
          // 分析规则通常不会修改内容，而是产生分析结果
          // 这里只是简单地返回原内容
          this.analyzeContent(content, rule);
          return content;
        case RuleType.GENERATION:
          return this.applyGenerationRule(content, rule);
        case RuleType.CUSTOM:
        default:
          // 自定义规则，尝试解析规则内容并应用
          return this.applyCustomRule(content, rule);
      }
    } catch (error) {
      console.error(`应用规则 ${rule.id} 时出错:`, error);
      return content; // 出错时返回原始内容
    }
  }
  
  /**
   * 应用转换规则
   * @param content 原始内容
   * @param rule 规则对象
   */
  private applyTransformationRule(content: string, rule: Rule): string {
    try {
      // 从规则内容中提取转换模式
      const patternMatch = rule.content.match(/Pattern:\s*\/(.+)\/([gimuy]*)/m);
      const replacementMatch = rule.content.match(/Replacement:\s*`?([^`]*)`?/m);
      
      if (patternMatch && replacementMatch) {
        const pattern = new RegExp(patternMatch[1], patternMatch[2] || '');
        const replacement = replacementMatch[1].replace(/\\n/g, '\n');
        
        // 应用正则表达式替换
        return content.replace(pattern, replacement);
      }
      
      return content;
    } catch (error) {
      console.error(`应用转换规则 ${rule.id} 时出错:`, error);
      return content;
    }
  }
  
  /**
   * 分析内容
   * @param content 内容
   * @param rule 规则对象
   */
  private analyzeContent(content: string, rule: Rule): void {
    try {
      // 从规则内容中提取分析模式
      const patternMatch = rule.content.match(/Pattern:\s*\/(.+)\/([gimuy]*)/m);
      
      if (patternMatch) {
        const pattern = new RegExp(patternMatch[1], patternMatch[2] || '');
        
        // 应用正则表达式查找
        const matches = content.match(pattern);
        if (matches) {
          console.log(`规则 ${rule.name} 分析结果:`, matches);
          // 这里可以触发事件或者记录分析结果
        }
      }
    } catch (error) {
      console.error(`分析内容 ${rule.id} 时出错:`, error);
    }
  }
  
  /**
   * 应用生成规则
   * @param content 原始内容
   * @param rule 规则对象
   */
  private applyGenerationRule(content: string, rule: Rule): string {
    try {
      // 从规则内容中提取生成模板
      const templateMatch = rule.content.match(/Template:\s*```([\s\S]+?)```/m);
      
      if (templateMatch) {
        const template = templateMatch[1];
        
        // 简单的变量替换
        // 例如，将 {{fileName}} 替换为实际的文件名
        let result = template;
        
        // 对于生成规则，可能需要根据上下文提供更复杂的变量处理
        // 这里只是一个简单的示例
        result = result.replace(/{{content}}/g, content);
        result = result.replace(/{{date}}/g, new Date().toISOString());
        
        return result;
      }
      
      return content;
    } catch (error) {
      console.error(`应用生成规则 ${rule.id} 时出错:`, error);
      return content;
    }
  }
  
  /**
   * 应用自定义规则
   * @param content 原始内容
   * @param rule 规则对象
   */
  private applyCustomRule(content: string, rule: Rule): string {
    try {
      // 自定义规则可以包含多种处理逻辑
      // 这里尝试解析规则内容中的所有模式和替换
      
      let result = content;
      
      // 查找所有的 Pattern/Replacement 对
      const patternRegex = /Pattern:\s*\/(.+)\/([gimuy]*)\s*\nReplacement:\s*`?([^`]*)`?/g;
      let match;
      
      // 依次应用所有的模式替换
      while ((match = patternRegex.exec(rule.content)) !== null) {
        try {
          const pattern = new RegExp(match[1], match[2] || '');
          const replacement = match[3].replace(/\\n/g, '\n');
          
          result = result.replace(pattern, replacement);
        } catch (e) {
          console.error(`应用模式 ${match[0]} 时出错:`, e);
        }
      }
      
      return result;
    } catch (error) {
      console.error(`应用自定义规则 ${rule.id} 时出错:`, error);
      return content;
    }
  }
} 