import * as vscode from 'vscode';
import { DiagnosticService, DiagnosticIssue, DiagnosticSeverity, DiagnosticSource } from './DiagnosticService';

/**
 * 批量修复配置接口
 */
export interface BatchFixConfig {
  /**
   * 要修复的文件URI
   */
  uri?: vscode.Uri;
  
  /**
   * 要修复的严重性列表
   */
  severities?: DiagnosticSeverity[];
  
  /**
   * 要修复的来源列表
   */
  sources?: DiagnosticSource[];
  
  /**
   * 是否只修复选中区域
   */
  fixOnlySelected?: boolean;
  
  /**
   * 修复前是否确认
   */
  confirmBeforeFix?: boolean;
  
  /**
   * 最大修复数量
   */
  maxFixCount?: number;
  
  /**
   * 修复超时时间（毫秒）
   */
  timeout?: number;
}

/**
 * 批量修复结果接口
 */
export interface BatchFixResult {
  /**
   * 成功修复的问题数量
   */
  fixedCount: number;
  
  /**
   * 失败的修复数量
   */
  failedCount: number;
  
  /**
   * 跳过的修复数量
   */
  skippedCount: number;
  
  /**
   * 修复的文件URI列表
   */
  fixedFiles: vscode.Uri[];
  
  /**
   * 处理时间（毫秒）
   */
  processingTime: number;
  
  /**
   * 详细的修复记录
   */
  details: BatchFixDetail[];
}

/**
 * 批量修复详情接口
 */
export interface BatchFixDetail {
  /**
   * 问题ID
   */
  issueId: string;
  
  /**
   * 问题消息
   */
  message: string;
  
  /**
   * 文件URI
   */
  file: vscode.Uri;
  
  /**
   * 修复结果
   */
  success: boolean;
  
  /**
   * 错误信息（如果有）
   */
  error?: string;
}

/**
 * 批量修复服务类
 * 用于处理多个问题的批量修复
 */
export class BatchFixService {
  private static instance: BatchFixService | undefined;
  private _diagnosticService: DiagnosticService;
  private _onBatchFixCompleted = new vscode.EventEmitter<BatchFixResult>();
  
  readonly onBatchFixCompleted = this._onBatchFixCompleted.event;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): BatchFixService {
    if (!BatchFixService.instance) {
      BatchFixService.instance = new BatchFixService();
    }
    return BatchFixService.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    this._diagnosticService = DiagnosticService.getInstance();
  }
  
  /**
   * 初始化批量修复服务
   * @param context 扩展上下文
   */
  public initialize(context: vscode.ExtensionContext): void {
    // 注册命令
    context.subscriptions.push(
      vscode.commands.registerCommand('lazycode.fixAllIssues', () => this.fixAllIssues())
    );
    
    context.subscriptions.push(
      vscode.commands.registerCommand('lazycode.fixSelectedIssues', () => this.fixSelectedIssues())
    );
    
    context.subscriptions.push(
      vscode.commands.registerCommand('lazycode.fixSpecificIssues', (config: BatchFixConfig) => this.fixIssues(config))
    );
  }
  
  /**
   * 修复所有问题
   */
  public async fixAllIssues(): Promise<BatchFixResult> {
    return this.fixIssues({});
  }
  
  /**
   * 修复选中区域内的问题
   */
  public async fixSelectedIssues(): Promise<BatchFixResult> {
    return this.fixIssues({ fixOnlySelected: true });
  }
  
  /**
   * 按配置修复问题
   * @param config 批量修复配置
   */
  public async fixIssues(config: BatchFixConfig): Promise<BatchFixResult> {
    const startTime = Date.now();
    const result: BatchFixResult = {
      fixedCount: 0,
      failedCount: 0,
      skippedCount: 0,
      fixedFiles: [],
      processingTime: 0,
      details: []
    };
    
    try {
      // 如果需要确认，显示确认对话框
      if (config.confirmBeforeFix) {
        const confirmation = await vscode.window.showWarningMessage(
          '确定要批量修复问题吗？此操作可能会修改多个文件。',
          { modal: true },
          '确定', '取消'
        );
        
        if (confirmation !== '确定') {
          return result;
        }
      }
      
      // 获取当前活动编辑器
      const editor = vscode.window.activeTextEditor;
      const uri = config.uri || (editor ? editor.document.uri : undefined);
      
      // 如果没有指定URI且没有活动编辑器，则无法继续
      if (!uri) {
        await vscode.window.showErrorMessage('没有打开的文件');
        return result;
      }
      
      // 获取选中范围（如果需要）
      let selectedRange: vscode.Range | undefined;
      if (config.fixOnlySelected && editor) {
        selectedRange = editor.selection;
        if (selectedRange.isEmpty) {
          await vscode.window.showInformationMessage('请先选择要修复的代码区域');
          return result;
        }
      }
      
      // 开始进度显示
      await vscode.window.withProgress(
        {
          location: vscode.ProgressLocation.Notification,
          title: '正在批量修复问题...',
          cancellable: true
        },
        async (progress, token) => {
          // 设置取消令牌
          token.onCancellationRequested(() => {
            return;
          });
          
          // 获取问题列表
          let issues = this._diagnosticService.getIssues(uri);
          
          // 根据配置过滤问题
          issues = this.filterIssues(issues, config, selectedRange);
          
          // 限制最大修复数量
          if (config.maxFixCount && issues.length > config.maxFixCount) {
            issues = issues.slice(0, config.maxFixCount);
          }
          
          // 检查是否有要修复的问题
          if (issues.length === 0) {
            await vscode.window.showInformationMessage('没有可修复的问题');
            return;
          }
          
          // 显示进度
          progress.report({ message: `发现 ${issues.length} 个问题需要修复` });
          
          // 记录已修复的文件
          const fixedFilesMap = new Map<string, vscode.Uri>();
          
          // 创建批量编辑
          const workspaceEdit = new vscode.WorkspaceEdit();
          
          // 处理每个问题
          for (let i = 0; i < issues.length; i++) {
            // 检查是否取消
            if (token.isCancellationRequested) {
              break;
            }
            
            const issue = issues[i];
            progress.report({ 
              message: `正在处理问题 ${i + 1}/${issues.length}: ${issue.message}`,
              increment: 100 / issues.length 
            });
            
            try {
              // 如果问题有修复建议
              if (issue.fixes && issue.fixes.length > 0) {
                // 获取第一个修复建议
                const fix = issue.fixes[0];
                
                // 应用修复
                if (this.applyFixToEdit(workspaceEdit, issue, fix)) {
                  // 记录成功
                  result.fixedCount++;
                  fixedFilesMap.set(issue.file.toString(), issue.file);
                  
                  result.details.push({
                    issueId: issue.id,
                    message: issue.message,
                    file: issue.file,
                    success: true
                  });
                } else {
                  // 记录失败
                  result.failedCount++;
                  
                  result.details.push({
                    issueId: issue.id,
                    message: issue.message,
                    file: issue.file,
                    success: false,
                    error: '无法应用修复'
                  });
                }
              } else {
                // 记录跳过
                result.skippedCount++;
                
                result.details.push({
                  issueId: issue.id,
                  message: issue.message,
                  file: issue.file,
                  success: false,
                  error: '没有可用的修复建议'
                });
              }
            } catch (error) {
              // 记录错误
              result.failedCount++;
              
              result.details.push({
                issueId: issue.id,
                message: issue.message,
                file: issue.file,
                success: false,
                error: error instanceof Error ? error.message : String(error)
              });
              
              console.error(`修复问题 ${issue.id} 时出错:`, error);
            }
            
            // 检查超时
            if (config.timeout && Date.now() - startTime > config.timeout) {
              await vscode.window.showWarningMessage('批量修复操作超时，已停止');
              break;
            }
          }
          
          // 应用编辑
          if (result.fixedCount > 0) {
            const success = await vscode.workspace.applyEdit(workspaceEdit);
            if (!success) {
              await vscode.window.showErrorMessage('应用修复失败');
              result.fixedCount = 0;
              result.failedCount += issues.length;
              return;
            }
            
            // 记录修复的文件
            result.fixedFiles = Array.from(fixedFilesMap.values());
            
            // 重新诊断已修复的文件
            for (const fileUri of result.fixedFiles) {
              const document = await vscode.workspace.openTextDocument(fileUri);
              await this._diagnosticService.diagnoseDocument(document);
            }
          }
        }
      );
      
      // 计算处理时间
      result.processingTime = Date.now() - startTime;
      
      // 显示结果
      if (result.fixedCount > 0) {
        await vscode.window.showInformationMessage(
          `已修复 ${result.fixedCount} 个问题，` +
          `失败 ${result.failedCount} 个，` +
          `跳过 ${result.skippedCount} 个`
        );
      } else if (result.failedCount > 0) {
        await vscode.window.showErrorMessage(
          `修复失败: ${result.failedCount} 个问题修复失败，` +
          `跳过 ${result.skippedCount} 个`
        );
      } else {
        await vscode.window.showInformationMessage('没有应用任何修复');
      }
      
      // 触发完成事件
      this._onBatchFixCompleted.fire(result);
      
      return result;
    } catch (error) {
      console.error('批量修复出错:', error);
      
      await vscode.window.showErrorMessage(
        '批量修复过程中发生错误: ' + (error instanceof Error ? error.message : String(error))
      );
      
      // 计算处理时间
      result.processingTime = Date.now() - startTime;
      
      // 触发完成事件
      this._onBatchFixCompleted.fire(result);
      
      return result;
    }
  }
  
  /**
   * 根据配置过滤问题
   * @param issues 问题列表
   * @param config 批量修复配置
   * @param selectedRange 选中范围
   */
  private filterIssues(
    issues: DiagnosticIssue[],
    config: BatchFixConfig,
    selectedRange?: vscode.Range
  ): DiagnosticIssue[] {
    // 根据严重性过滤
    if (config.severities && config.severities.length > 0) {
      issues = issues.filter(issue => config.severities?.includes(issue.severity));
    }
    
    // 根据来源过滤
    if (config.sources && config.sources.length > 0) {
      issues = issues.filter(issue => config.sources?.includes(issue.source));
    }
    
    // 根据选中范围过滤
    if (config.fixOnlySelected && selectedRange && !selectedRange.isEmpty) {
      issues = issues.filter(issue => {
        return selectedRange.contains(issue.range) || issue.range.intersection(selectedRange);
      });
    }
    
    // 按严重性排序
    issues = issues.sort((a, b) => {
      const severityOrder = {
        [DiagnosticSeverity.ERROR]: 0,
        [DiagnosticSeverity.WARNING]: 1,
        [DiagnosticSeverity.HINT]: 2,
        [DiagnosticSeverity.INFO]: 3
      };
      
      return severityOrder[a.severity] - severityOrder[b.severity];
    });
    
    return issues;
  }
  
  /**
   * 将修复应用到编辑
   * @param edit 工作区编辑
   * @param issue 问题
   * @param fix 修复建议
   */
  private applyFixToEdit(
    edit: vscode.WorkspaceEdit,
    issue: DiagnosticIssue,
    fix: any
  ): boolean {
    try {
      // 检查修复是否有效
      if (!fix || !fix.action) {
        return false;
      }
      
      const { action } = fix;
      
      // 根据操作类型执行不同的编辑
      switch (action.type) {
        case 'replace':
          if (action.text !== undefined) {
            const range = new vscode.Range(action.start, action.end);
            edit.replace(issue.file, range, action.text);
            return true;
          }
          break;
          
        case 'delete':
          const deleteRange = new vscode.Range(action.start, action.end);
          edit.delete(issue.file, deleteRange);
          return true;
          
        case 'insert':
          if (action.text !== undefined) {
            const position = action.start;
            edit.insert(issue.file, position, action.text);
            return true;
          }
          break;
          
        case 'multiple':
          if (action.actions) {
            // 处理多重操作
            for (const subAction of action.actions) {
              switch (subAction.type) {
                case 'replace':
                  if (subAction.text !== undefined) {
                    const range = new vscode.Range(subAction.start, subAction.end);
                    edit.replace(issue.file, range, subAction.text);
                  }
                  break;
                  
                case 'delete':
                  const deleteRange = new vscode.Range(subAction.start, subAction.end);
                  edit.delete(issue.file, deleteRange);
                  break;
                  
                case 'insert':
                  if (subAction.text !== undefined) {
                    const position = subAction.start;
                    edit.insert(issue.file, position, subAction.text);
                  }
                  break;
              }
            }
            return true;
          }
          break;
      }
      
      return false;
    } catch (error) {
      console.error('应用修复到编辑失败:', error);
      return false;
    }
  }
} 