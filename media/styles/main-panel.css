:root {
  --background-color: var(--vscode-editor-background);
  --foreground-color: var(--vscode-editor-foreground);
  --header-background: var(--vscode-activityBar-background);
  --header-foreground: var(--vscode-activityBar-foreground);
  --button-background: var(--vscode-button-background);
  --button-foreground: var(--vscode-button-foreground);
  --button-hover-background: var(--vscode-button-hoverBackground);
}

body {
  background-color: var(--background-color);
  color: var(--foreground-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

header {
  background-color: var(--header-background);
  color: var(--header-foreground);
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

main {
  flex: 1;
  padding: 2rem;
  overflow: auto;
}

.welcome {
  margin-bottom: 2rem;
}

.welcome h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 500;
}

button {
  background-color: var(--button-background);
  color: var(--button-foreground);
  border: none;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  border-radius: 2px;
  cursor: pointer;
  margin-top: 1rem;
}

button:hover {
  background-color: var(--button-hover-background);
}

button:focus {
  outline: 1px solid var(--button-foreground);
} 