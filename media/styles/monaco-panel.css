:root {
  --background-color: var(--vscode-editor-background);
  --foreground-color: var(--vscode-editor-foreground);
  --header-background: var(--vscode-activityBar-background);
  --header-foreground: var(--vscode-activityBar-foreground);
  --button-background: var(--vscode-button-background);
  --button-foreground: var(--vscode-button-foreground);
  --button-hover-background: var(--vscode-button-hoverBackground);
  --secondary-button-background: var(--vscode-button-secondaryBackground);
  --secondary-button-foreground: var(--vscode-button-secondaryForeground);
  --editor-background: var(--vscode-editor-background);
  --status-bar-background: var(--vscode-statusBar-background);
  --status-bar-foreground: var(--vscode-statusBar-foreground);
  --select-background: var(--vscode-dropdown-background);
  --select-foreground: var(--vscode-dropdown-foreground);
  --select-border: var(--vscode-dropdown-border);
}

body {
  background-color: var(--background-color);
  color: var(--foreground-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

header {
  background-color: var(--header-background);
  color: var(--header-foreground);
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

main {
  flex: 1;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

#editor-container {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 300px;
  border: none;
  position: relative;
}

footer {
  background-color: var(--status-bar-background);
  color: var(--status-bar-foreground);
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#language-status,
#position-status,
#intellisense-status {
  padding: 0 0.5rem;
}

#intellisense-status {
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
}

#intellisense-status:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

#intellisense-status.enabled {
  color: var(--vscode-terminal-ansiGreen);
}

#intellisense-status.disabled {
  color: var(--vscode-terminal-ansiRed);
}

button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 0.9rem;
}

button#applyChanges {
  background-color: var(--button-background);
  color: var(--button-foreground);
}

button#applyChanges:hover {
  background-color: var(--button-hover-background);
}

button#refreshContent {
  background-color: var(--secondary-button-background);
  color: var(--secondary-button-foreground);
}

button:focus {
  outline: 1px solid var(--vscode-focusBorder);
}

/* 语言选择器样式 */
select#languageSelector {
  padding: 0.3rem 0.5rem;
  background-color: var(--select-background);
  color: var(--select-foreground);
  border: 1px solid var(--select-border);
  border-radius: 2px;
  font-size: 0.9rem;
  outline: none;
}

select#languageSelector:focus {
  outline: 1px solid var(--vscode-focusBorder);
}

select#languageSelector option {
  background-color: var(--select-background);
  color: var(--select-foreground);
}

/* 加载中状态 */
.loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid var(--button-background);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 