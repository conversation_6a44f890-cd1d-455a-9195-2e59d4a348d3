:root {
  --background-color: var(--vscode-editor-background);
  --foreground-color: var(--vscode-editor-foreground);
  --header-background: var(--vscode-activityBar-background);
  --header-foreground: var(--vscode-activityBar-foreground);
  --button-background: var(--vscode-button-background);
  --button-foreground: var(--vscode-button-foreground);
  --button-hover-background: var(--vscode-button-hoverBackground);
  --secondary-button-background: var(--vscode-button-secondaryBackground);
  --secondary-button-foreground: var(--vscode-button-secondaryForeground);
  --code-background: var(--vscode-editor-inactiveSelectionBackground);
}

body {
  background-color: var(--background-color);
  color: var(--foreground-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

header {
  background-color: var(--header-background);
  color: var(--header-foreground);
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

main {
  flex: 1;
  padding: 1rem;
  overflow: auto;
}

.suggestions {
  padding: 0.5rem;
}

.suggestion-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-editor-background);
}

.suggestion-item h3 {
  margin-top: 0;
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

.suggestion-item .description {
  margin-top: 0;
  margin-bottom: 0.8rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.code-snippet {
  background-color: var(--code-background);
  border-radius: 3px;
  padding: 0.8rem;
  overflow-x: auto;
  font-family: 'SF Mono', Monaco, Menlo, Consolas, 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;
  white-space: pre-wrap;
}

.actions {
  display: flex;
  gap: 0.5rem;
}

button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 0.9rem;
}

.apply-btn {
  background-color: var(--button-background);
  color: var(--button-foreground);
}

.apply-btn:hover {
  background-color: var(--button-hover-background);
}

.reject-btn {
  background-color: var(--secondary-button-background);
  color: var(--secondary-button-foreground);
}

button:focus {
  outline: 1px solid var(--vscode-focusBorder);
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: var(--vscode-descriptionForeground);
}

.empty-state p {
  margin-bottom: 1rem;
  font-size: 1rem;
} 