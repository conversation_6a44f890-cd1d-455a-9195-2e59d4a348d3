:root {
  --background-color: var(--vscode-editor-background);
  --foreground-color: var(--vscode-editor-foreground);
  --header-background: var(--vscode-activityBar-background);
  --header-foreground: var(--vscode-activityBar-foreground);
  --button-background: var(--vscode-button-background);
  --button-foreground: var(--vscode-button-foreground);
  --button-hover-background: var(--vscode-button-hoverBackground);
  --secondary-button-background: var(--vscode-button-secondaryBackground);
  --secondary-button-foreground: var(--vscode-button-secondaryForeground);
  --memory-background: var(--vscode-sideBar-background);
  --memory-border: var(--vscode-panel-border);
  --status-bar-background: var(--vscode-statusBar-background);
  --status-bar-foreground: var(--vscode-statusBar-foreground);
  --input-background: var(--vscode-input-background);
  --input-foreground: var(--vscode-input-foreground);
  --input-border: var(--vscode-input-border);
}

body {
  background-color: var(--background-color);
  color: var(--foreground-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

header {
  background-color: var(--header-background);
  color: var(--header-foreground);
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

main {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  gap: 1rem;
}

.sidebar {
  width: 250px;
  flex-shrink: 0;
  background-color: var(--memory-background);
  border-radius: 4px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.content {
  flex: 1;
  overflow-y: auto;
}

/* 记忆过滤器 */
.memory-filter {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.filter-btn {
  padding: 0.3rem 0.8rem;
  width: 100%;
  text-align: left;
}

.language-filter {
  margin-top: 0.5rem;
}

.language-filter label {
  display: block;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.language-filter select {
  width: 100%;
  padding: 0.4rem;
  background-color: var(--input-background);
  color: var(--input-foreground);
  border: 1px solid var(--input-border);
  border-radius: 3px;
}

.sort-options {
  margin-top: 0.5rem;
}

.sort-options label {
  display: block;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.sort-options select {
  width: 100%;
  padding: 0.4rem;
  background-color: var(--input-background);
  color: var(--input-foreground);
  border: 1px solid var(--input-border);
  border-radius: 3px;
  margin-bottom: 0.5rem;
}

/* 记忆分类 */
.memory-category {
  margin-bottom: 2rem;
}

.memory-category h2 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid var(--memory-border);
  padding-bottom: 0.5rem;
}

.memory-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

/* 记忆项 */
.memory-item {
  background-color: var(--memory-background);
  border: 1px solid var(--memory-border);
  border-radius: 4px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.memory-item:hover {
  border-color: var(--button-background);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.memory-item.starred {
  border-left: 3px solid gold;
}

.memory-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.memory-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.memory-controls {
  display: flex;
  gap: 0.3rem;
}

.memory-controls button {
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.8rem;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.star-btn {
  background-color: transparent;
  color: var(--foreground-color);
}

.star-btn.starred {
  color: gold;
}

.apply-btn {
  background-color: var(--button-background);
  color: var(--button-foreground);
}

.edit-btn {
  background-color: var(--secondary-button-background);
  color: var(--secondary-button-foreground);
}

.delete-btn {
  background-color: var(--vscode-errorForeground);
  color: white;
}

.description {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: var(--foreground-color);
  opacity: 0.8;
}

.content {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  border-radius: 3px;
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 0.8rem;
  margin: 0.5rem 0;
  white-space: pre-wrap;
  max-height: 150px;
  overflow-y: auto;
  flex: 1;
}

.memory-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.8rem;
}

.tag {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  display: inline-block;
}

.usage, .last-used {
  opacity: 0.7;
}

/* 搜索框 */
#searchInput {
  padding: 0.5rem;
  border-radius: 3px;
  border: 1px solid var(--input-border);
  background-color: var(--input-background);
  color: var(--input-foreground);
  width: 200px;
  font-size: 0.9rem;
  outline: none;
}

#searchInput:focus {
  border-color: var(--button-background);
}

/* 按钮样式 */
button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 0.9rem;
}

header button {
  background-color: var(--button-background);
  color: var(--button-foreground);
}

header button:hover {
  background-color: var(--button-hover-background);
}

button:focus {
  outline: 1px solid var(--vscode-focusBorder);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-state p {
  margin-bottom: 1rem;
  opacity: 0.7;
}

/* 记忆编辑器模态框 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: auto;
}

.modal-content {
  background-color: var(--background-color);
  margin: 5% auto;
  padding: 0;
  border: 1px solid var(--memory-border);
  border-radius: 5px;
  width: 80%;
  max-width: 800px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background-color: var(--header-background);
  color: var(--header-foreground);
  padding: 1rem;
  border-bottom: 1px solid var(--memory-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 500;
}

.close-btn {
  color: var(--header-foreground);
  float: right;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
}

.modal-body {
  padding: 1rem;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input[type="text"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border-radius: 3px;
  border: 1px solid var(--input-border);
  background-color: var(--input-background);
  color: var(--input-foreground);
  font-size: 0.9rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-group input[type="checkbox"] {
  margin-right: 0.5rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.form-actions button[type="submit"] {
  background-color: var(--button-background);
  color: var(--button-foreground);
}

.form-actions button[type="button"] {
  background-color: var(--secondary-button-background);
  color: var(--secondary-button-foreground);
}

/* 标签云样式 */
.common-tags {
  margin-bottom: 1rem;
}

.common-tags h3 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-item {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.tag-item:hover {
  background-color: var(--button-background);
  color: var(--button-foreground);
}

.tag-item small {
  opacity: 0.7;
}

/* 过滤部分样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.filter-section h3 {
  font-size: 1rem;
  margin-bottom: 0.2rem;
  font-weight: 500;
}

/* 高级搜索样式 */
#advancedSearchBtn {
  background-color: var(--secondary-button-background);
  color: var(--secondary-button-foreground);
}

/* 媒体查询 */
@media (max-width: 768px) {
  .memory-list {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 95%;
    margin: 5% auto;
  }
}

/* 媒体查询 - 响应式设计 */
@media (max-width: 900px) {
  main {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .memory-filter {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .filter-btn {
    width: auto;
  }
} 