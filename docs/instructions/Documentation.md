# LazyCode 项目文档

## 项目概述

LazyCode是一个Visual Studio Code扩展项目，旨在复刻和改进Augment扩展的功能。该项目是基于VSCode扩展框架构建的，使用TypeScript作为主要开发语言。

## 目标功能

根据对原始Augment扩展的研究，我们计划实现以下功能：

1. **Next Edit功能**：提供智能的下一步编辑建议
2. **Monaco编辑器集成**：在扩展面板中嵌入Monaco编辑器
3. **主面板**：创建交互式用户界面
4. **规则系统**：支持自定义规则配置
5. **记忆功能**：存储和访问先前的交互或上下文
6. **自动修复功能**：自动修复代码问题
7. **差异视图**：比较和可视化代码变更
8. **多语言支持**：支持多种编程语言的语法高亮和分析

## 组件说明

### NextEditService

NextEditService是LazyCode扩展的核心服务类，负责生成和管理编辑建议。主要功能包括：

- 获取当前活动编辑器的内容和上下文
- 分析代码结构和上下文，提供相关的编辑建议
- 管理编辑建议的生命周期和状态
- 将选定的编辑建议应用到编辑器中
- 记录编辑历史并支持撤销操作
- 提供建议变更事件，允许UI组件响应变化

#### EditSuggestion接口

```typescript
interface EditSuggestion {
  id: string;           // 建议的唯一标识符
  title: string;        // 建议的标题
  description?: string; // 建议的详细描述(可选)
  code: string;         // 建议的代码内容
  language: string;     // 代码的语言
  range?: vscode.Range; // 应用编辑的范围(可选，默认为光标位置)
}
```

#### EditHistoryItem接口

```typescript
interface EditHistoryItem {
  id: string;           // 历史记录的唯一标识符
  suggestionId: string; // 关联的建议ID
  timestamp: number;    // 应用时间戳
  documentUri: string;  // 文档URI
  range: vscode.Range;  // 编辑范围
  originalText: string; // 原始文本
  newText: string;      // 新文本
}
```

### NextEditPanel

NextEditPanel是显示编辑建议的WebView面板，负责：

- 以用户友好的界面显示编辑建议
- 提供交互控件（应用、拒绝、刷新等）
- 将用户操作转发给NextEditService处理
- 动态更新UI以反映建议状态的变化

### WebViewProvider

WebViewProvider是所有WebView面板的基类，提供通用功能：

- 创建和管理WebView
- 处理WebView生命周期事件
- 提供与扩展通信的机制
- 处理资源加载和内容安全策略

## 复刻实施方法

我们采用的复刻方法遵循以下原则：

1. **渐进式开发**：从基础功能开始，逐步实现复杂功能
2. **持续测试**：每个功能节点完成后进行测试，确保代码正常运行
3. **反向工程驱动**：通过分析原始扩展的结构和资源文件，逆向理解实现方式
4. **模块化实现**：将复杂功能分解为独立模块，降低复杂度

## 复刻阶段划分

复刻工作分为以下几个主要阶段：

1. **基础架构复刻**：配置、资源和基础WebView框架
2. **核心功能复刻**：扩展激活、Next Edit功能和基础通信
3. **Monaco编辑器集成**：编辑器加载和语言服务
4. **高级功能复刻**：规则系统、记忆功能、自动修复和差异视图
5. **测试与优化**：全功能测试、性能优化和发布准备

## 技术栈

- TypeScript
- VSCode扩展API
- esbuild (用于构建)
- Monaco编辑器

## 文件结构

```
lazycode/
├── src/                  # 源代码目录
│   ├── extension.ts      # 扩展入口点
│   ├── webviews/         # WebView相关代码
│   ├── services/         # 服务类和业务逻辑
│   ├── utils/            # 工具函数
│   └── test/             # 测试目录
├── media/                # 媒体资源
│   ├── keyboard/         # 键盘相关资源
│   └── next-edit/        # 编辑建议相关资源
├── docs/                 # 文档目录
│   ├── instructions/     # 指令和指南
│   └── project_management/ # 项目管理文档
├── .vscode/             # VSCode配置
└── package.json         # 项目配置
```

## 参考资料

本项目参考了origin目录中的Augment扩展，该扩展是一个功能完善的AI辅助编程工具。通过研究其结构和功能，我们可以更好地理解如何实现类似功能。

### 关键参考文件

- `origin/package.json` - 扩展配置和贡献点
- `origin/common-webviews/` - WebView HTML模板
- `origin/media/` - 图标和视觉资源
- `origin/extension-beautified.js` - 主要扩展逻辑 

## 特性说明

### 智能编辑建议

LazyCode能够分析代码上下文，根据当前编辑环境提供相关的编辑建议：

1. **上下文感知**：分析当前代码结构，识别是否位于函数、类、循环或条件语句内部
2. **语言特定建议**：为不同编程语言(JavaScript/TypeScript、HTML、CSS、JSON等)提供专门的建议
3. **智能变量引用**：识别当前作用域中的变量，并在建议中合理引用
4. **代码结构感知**：根据代码结构提供适当的建议，如在函数内部提供返回语句，在类内部提供方法定义

### 编辑历史记录

LazyCode保存编辑操作的历史记录，支持撤销已应用的建议：

1. **历史跟踪**：记录每个应用的编辑建议，包括原始文本和新文本
2. **撤销支持**：一键撤销最近的编辑操作，恢复到原始状态
3. **跨会话持久化**：（计划中）支持编辑历史的持久化存储
4. **历史浏览**：（计划中）浏览和选择性应用历史编辑

### 多语言支持

LazyCode提供对多种编程语言的支持，包括：

1. **JavaScript/TypeScript**：提供函数、类、方法等建议
2. **HTML**：提供元素、表单、导航等建议
3. **CSS**：提供布局、响应式设计等建议
4. **JSON**：提供配置对象、数组等建议
5. **其他语言**：提供通用注释和基础结构建议 

### 诊断服务

DiagnosticService是LazyCode的代码问题诊断服务，负责检测代码问题并生成修复建议：

1. **多级诊断**：支持错误(ERROR)、警告(WARNING)、提示(HINT)和信息(INFO)四个级别的诊断
2. **多源诊断**：支持基于规则(RULE)、语法(SYNTAX)、样式(STYLE)、性能(PERFORMANCE)和安全(SECURITY)的诊断
3. **VSCode集成**：将诊断问题集成到VSCode的Problems面板
4. **实时诊断**：在文档变化时自动运行诊断
5. **自动修复**：为检测到的问题提供自动修复建议

#### DiagnosticIssue接口

```typescript
interface DiagnosticIssue {
  id: string;                 // 问题ID
  message: string;            // 问题消息
  severity: DiagnosticSeverity; // 问题严重性
  source: DiagnosticSource;   // 问题来源
  range: vscode.Range;        // 问题范围
  file: vscode.Uri;           // 问题所在文件
  fixes: FixSuggestion[];     // 修复建议
  ruleId?: string;            // 相关规则ID
  context?: string;           // 代码上下文
  timestamp: number;          // 问题发现时间
}
```

#### FixSuggestion接口

```typescript
interface FixSuggestion {
  id: string;           // 建议ID
  title: string;        // 建议标题
  description: string;  // 建议描述
  action: FixAction;    // 修复操作
  beforePreview?: string; // 修复前预览
  afterPreview?: string;  // 修复后预览
}
```

### 修复面板

FixPanel是显示和管理诊断问题的面板，提供以下功能：

1. **问题展示**：按文件和严重性分组显示诊断问题
2. **问题过滤**：按严重性和问题来源过滤问题
3. **修复预览**：在应用修复前预览修改效果
4. **一键修复**：快速应用修复建议
5. **批量修复**：一次性修复多个问题
6. **问题导航**：快速跳转到问题位置 

## 诊断服务增强

LazyCode的诊断服务现已支持多种编程语言，并提供上下文感知的智能修复建议。主要增强包括：

### 多语言支持

1. **JavaScript/TypeScript**
   - 语法检查：未闭合的括号、引号，变量名拼写错误
   - 风格检查：行长度限制，命名规范，冗余标点符号
   - 上下文感知修复：根据代码上下文提供更精确的修复选项

2. **Python**
   - PEP 8标准检查：缩进一致性，行长度限制
   - 语法检查：未闭合的括号，缩进错误
   - 风格建议：遵循Python编码规范的建议

3. **Markdown**
   - 结构检查：未闭合的代码块，标题层级不一致
   - 内容检查：空链接，空图片引用
   - 格式修复：自动补全缺失的标记和结构

4. **JSON**
   - 语法验证：缺失的逗号，不匹配的括号
   - 格式检查：多余的逗号，不合规的值
   - 智能修复：根据JSON规范提供精确修复

5. **HTML/XML**
   - 标签匹配检查：未闭合的标签，标签不匹配
   - 属性验证：缺少必要属性，属性值格式不正确
   - 结构修复：自动闭合标签，修复不匹配的标签

### 上下文感知修复

诊断服务现在能够基于代码上下文生成更智能的修复建议：

1. **括号闭合建议**：根据代码结构提供多种闭合选项，如行尾闭合或就近闭合
2. **命名规范修复**：自动将变量名转换为适合当前语言的命名规范（如驼峰命名法）
3. **行长度优化**：智能在适当位置（如运算符处）拆分过长的代码行
4. **语法错误修正**：根据上下文提供更精准的语法错误修复，如拼写错误纠正

### 批量修复和选择性修复

1. **批量修复功能**：一键修复同类型或同严重级别的多个问题
2. **选中区域修复**：仅修复编辑器选中区域内的问题
3. **修复优先级**：先修复严重错误，再处理警告和提示 

## 安装和设置

1. 在VSCode扩展市场搜索"LazyCode"
2. 点击安装按钮
3. 重启VSCode以激活扩展
4. 通过状态栏中的"LazyCode"图标或命令面板访问功能

## 使用指南

### 智能编辑建议

智能编辑建议功能可以分析当前代码上下文，提供相关的编辑建议。

- 使用命令面板或快捷键打开编辑建议面板
- 在面板中查看建议列表
- 点击应用按钮将建议应用到代码中
- 使用撤销功能取消上一次应用的编辑

### Monaco编辑器

Monaco编辑器提供了在扩展内编辑代码的功能。

- 使用命令面板打开Monaco编辑器
- 从当前活动编辑器加载内容
- 使用内置的智能提示和语法高亮功能
- 将编辑后的内容保存回原文件

### 代码规则系统

代码规则系统允许用户定义和应用代码处理规则。

- 使用命令面板或侧边栏访问规则面板
- 创建新规则或编辑现有规则
- 将规则应用到当前文件
- 使用批量应用功能将多个规则应用到文件

### 代码记忆功能

代码记忆功能可以存储和应用常用的代码片段和解决方案。

- 使用命令面板或侧边栏访问记忆面板
- 捕获当前代码到记忆
- 搜索和过滤记忆
- 将记忆应用到当前编辑器
- 使用标签和语言过滤器组织记忆

### 代码自动修复

代码自动修复功能可以检测和修复代码中的问题。

- 使用命令面板或侧边栏访问修复面板
- 诊断当前文件中的问题
- 查看问题列表和修复建议
- 应用单个修复或批量修复
- 使用问题导航功能快速跳转到问题位置

#### 批量修复功能

批量修复功能允许用户一次性修复多个代码问题。

- 在修复面板中使用"一键修复"按钮修复所有问题
- 使用"修复选中区域"按钮只修复选中代码中的问题
- 通过右键菜单或命令面板使用批量修复命令
- 配置批量修复选项，如严重性过滤、来源过滤等
- 查看批量修复结果报告

### 代码差异比较

代码差异比较功能可以可视化展示代码的不同版本之间的差异。

- 使用命令面板或侧边栏打开差异比较面板
- 选择比较方式（与剪贴板、与上一版本、两个文件）
- 选择比较模式（按行、按单词、按字符）
- 查看高亮显示的差异内容
- 查看差异统计信息（添加、删除、未变更的行数）

## 命令列表

LazyCode提供以下命令，可以通过命令面板(Ctrl+Shift+P)访问：

- `LazyCode: 显示主面板` - 打开LazyCode主面板
- `LazyCode: 显示下一步编辑建议` - 显示编辑建议面板
- `LazyCode: 刷新编辑建议` - 刷新当前的编辑建议
- `LazyCode: 撤销上一次编辑` - 撤销最近应用的编辑
- `LazyCode: 打开Monaco编辑器` - 打开内置编辑器
- `LazyCode: 从活动编辑器加载内容` - 将当前编辑器内容加载到Monaco编辑器
- `LazyCode: 显示代码规则` - 打开规则面板
- `LazyCode: 创建新规则` - 创建新的代码规则
- `LazyCode: 刷新规则` - 重新加载规则列表
- `LazyCode: 应用规则` - 应用选定的规则
- `LazyCode: 应用所有规则` - 应用所有适用的规则
- `LazyCode: 显示代码记忆` - 打开记忆面板
- `LazyCode: 捕获当前代码到记忆` - 将当前代码保存为记忆
- `LazyCode: 应用记忆` - 应用选定的记忆
- `LazyCode: 显示代码修复面板` - 打开修复面板
- `LazyCode: 诊断当前文件` - 检测当前文件中的问题
- `LazyCode: 修复所有问题` - 修复当前文件中的所有问题
- `LazyCode: 修复选中区域的问题` - 修复选中代码中的问题
- `LazyCode: 显示差异比较面板` - 打开差异比较面板
- `LazyCode: 与剪贴板比较` - 比较编辑器内容和剪贴板内容
- `LazyCode: 与上一版本比较` - 比较当前文件和Git中的上一版本
- `LazyCode: 比较两个文件` - 选择任意两个文件进行比较

## 配置选项

LazyCode提供以下配置选项，可以在设置中进行调整：

- `lazycode.enableNextEditSuggestions` - 启用下一步编辑建议功能
- `lazycode.enableEmptyFileHint` - 在打开空文件时显示提示
- `lazycode.monacoEditorTheme` - Monaco编辑器主题
- `lazycode.rulesDirectory` - 代码规则目录的相对路径
- `lazycode.memoriesDirectory` - 代码记忆目录的相对路径

## 常见问题

### 为什么我看不到编辑建议？

编辑建议功能需要分析当前代码上下文，如果文件为空或内容过少，可能无法生成有意义的建议。

### 如何创建自定义规则？

1. 打开规则面板
2. 点击"创建新规则"按钮
3. 填写规则名称、描述和类型
4. 编写规则逻辑
5. 保存规则

### 如何组织和管理记忆？

记忆功能支持标签和语言过滤，您可以在捕获记忆时添加相关标签，并使用搜索和过滤功能快速找到需要的记忆。

### 批量修复功能如何工作？

批量修复功能会分析当前文件中的所有问题，并尝试应用自动修复。您可以选择修复所有问题或只修复选中区域内的问题，还可以通过配置选项过滤要修复的问题类型。

### 如何比较两个不同的文件版本？

您可以使用差异比较功能中的"比较两个文件"选项，选择要比较的两个文件后，差异比较面板会显示它们之间的差异。您也可以使用"与上一版本比较"功能，比较当前文件和Git仓库中的上一个版本。

## 故障排除

如果您遇到问题，请尝试以下步骤：

1. 检查VSCode控制台中的错误信息
2. 重新加载VSCode窗口
3. 确保您的项目结构符合预期
4. 检查配置选项是否正确

如果问题仍然存在，请提交问题报告。 

## 差异比较服务

DiffService是LazyCode的代码差异比较服务，负责分析和可视化代码变更：

1. **多种比较模式**：支持按行(LINE)、按单词(WORD)和按字符(CHARACTER)的比较
2. **多种比较方式**：支持与剪贴板比较、与上一版本比较和两个文件比较
3. **差异可视化**：通过表格视图（行比较）和内联视图（单词/字符比较）展示差异
4. **统计信息**：显示添加、删除和未变更的行/单词/字符数
5. **版本控制集成**：与Git集成，支持与上一版本比较

#### DiffResult接口

```typescript
interface DiffResult {
  chunks: DiffChunk[];        // 差异片段列表
  stats: {                    // 差异统计信息
    added: number;            // 添加的行/单词/字符数
    removed: number;          // 删除的行/单词/字符数
    unchanged: number;        // 未变更的行/单词/字符数
  };
  oldText: string;            // 原始文本
  newText: string;            // 新文本
  mode: DiffMode;             // 比较模式
  timestamp: number;          // 生成时间
}
```

#### DiffChunk接口

```typescript
interface DiffChunk {
  type: DiffType;             // 差异类型：ADDED、REMOVED或UNCHANGED
  content: string;            // 差异内容
  lineInfo?: {                // 行号信息（可选）
    oldStart?: number;        // 原始文本的起始行号
    oldEnd?: number;          // 原始文本的结束行号
    newStart?: number;        // 新文本的起始行号
    newEnd?: number;          // 新文本的结束行号
  };
}
```

### 差异比较面板

DiffPanel是显示代码差异比较结果的面板，提供以下功能：

1. **多种视图**：根据比较模式自动选择表格视图或内联视图
2. **统计信息**：显示添加、删除和未变更的内容数量
3. **比较模式切换**：允许用户切换不同的比较粒度
4. **高亮显示**：使用不同的颜色标记添加、删除和未变更的内容
5. **操作按钮**：提供快速访问不同比较方式的按钮

### 差异视图提供者

DiffViewProvider是在侧边栏显示差异比较选项的树视图提供者，包含以下功能：

1. **差异比较面板**：打开主差异比较面板
2. **文件比较选项**：比较两个文件
3. **代码比较选项**：与剪贴板比较、与上一版本比较 