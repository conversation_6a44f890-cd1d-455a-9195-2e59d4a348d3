# LazyCode 项目指令

## 开发指南

本文档提供了有关如何在LazyCode项目中工作的指导。

### 环境设置

1. 确保安装了Node.js (推荐v18.15.0或更高版本)和pnpm 9
2. 克隆仓库并执行以下命令安装依赖:
   ```bash
   pnpm install
   ```
3. 安装推荐的VSCode扩展：
   - amodio.tsl-problem-matcher
   - ms-vscode.extension-test-runner
   - dbaeumer.vscode-eslint

### 构建和测试

- 使用以下命令构建扩展:
  ```bash
  pnpm run compile
  ```
- 使用以下命令在监视模式下构建:
  ```bash
  pnpm run watch
  ```
- 按F5启动调试会话，将在新窗口中加载扩展
- 在命令面板中输入"Hello World"来测试基本功能

### 代码规范

- 使用TypeScript编写所有代码
- 遵循现有的代码风格和命名约定
- 确保代码通过linter检查: `pnpm run lint`
- 添加适当的类型注释
- 为新功能编写测试

## 复刻实施指南

按照以下指南进行Augment扩展的复刻工作。每个阶段完成后必须进行测试，确保功能正常运行。

### 第一阶段：基础架构复刻

#### 1. 扩展配置复刻
1. 分析 `origin/package.json` 中的contributes部分
2. 复制并调整所需的命令、菜单和快捷键定义
3. 提取配置项并适当简化
4. **测试点**: 使用F5启动扩展，确认无错误日志

#### 2. 资源复制与验证
1. 创建资源目录结构
2. 复制next-edit图标资源
3. 复制基本的媒体资源
4. **测试点**: 验证资源路径正确，无404错误

#### 3. 基础WebView实现
1. 创建WebViewProvider基类
2. 实现资源加载机制
3. 创建简单的HTML模板
4. **测试点**: 显示最简单的WebView面板并验证功能

### 第二阶段：核心功能复刻

#### 1. 扩展激活与命令
1. 修改extension.ts激活逻辑
2. 注册基本命令
3. 添加WebView创建命令
4. **测试点**: 验证命令注册和调用

#### 2. 通信机制实现
1. 设计消息类型和接口
2. 实现发送和接收函数
3. 添加消息处理器
4. **测试点**: 测试扩展和WebView双向通信

#### 3. Next Edit基础功能
1. 创建NextEditService
2. 实现基本的编辑建议数据结构
3. 添加命令触发机制
4. **测试点**: 验证编辑建议的触发

#### 4. Next Edit界面
1. 创建NextEditPanel
2. 实现编辑建议展示
3. 添加用户交互组件
4. **测试点**: 验证编辑建议的显示和交互

#### 5. 编辑应用功能
1. 实现编辑操作应用
2. 添加撤销功能
3. 实现编辑历史记录
4. **测试点**: 验证编辑应用和撤销

### 第三阶段：Monaco编辑器集成

#### 1. Monaco基础集成
1. 提取Monaco加载脚本
2. 创建编辑器容器
3. 实现基本配置
4. **测试点**: 验证编辑器加载和基本功能

#### 2. 语言服务集成
1. 添加语法高亮支持
2. 实现基本的自动完成
3. 添加错误检测
4. **测试点**: 验证语言特性功能

### 第四阶段：高级功能复刻

#### 1. 规则系统
1. 设计规则数据模型
2. 创建规则存储服务
3. 实现规则编辑界面
4. 添加规则应用逻辑
5. **测试点**: 验证规则编辑、保存和应用

#### 2. 记忆功能
1. 设计记忆数据结构
2. 创建持久化存储
3. 实现记忆管理界面
4. 添加记忆搜索和应用
5. **测试点**: 验证记忆存储、检索和应用

#### 3. 自动修复功能
1. 实现问题诊断
2. 创建修复建议服务
3. 添加修复应用机制
4. **测试点**: 验证问题诊断和修复应用

#### 4. 差异视图
1. 创建差异比较引擎
2. 实现差异可视化
3. 添加差异导航和应用
4. **测试点**: 验证差异比较和显示

### 第五阶段：测试与优化

#### 1. 全功能集成测试
1. 设计端到端测试场景
2. 实现测试用例
3. 执行综合测试
4. **测试点**: 验证所有功能协同工作

#### 2. 性能优化
1. 识别性能瓶颈
2. 优化资源加载
3. 提升响应速度
4. **测试点**: 验证性能改进效果

#### 3. 发布准备
1. 完善文档
2. 更新README
3. 准备发布材料
4. **测试点**: 最终验收测试

## 从原始代码复刻的具体指南

### Next Edit功能

从以下文件分析和复刻Next Edit功能：
1. `origin/common-webviews/next-edit-suggestions.html` - 界面结构
2. `origin/media/next-edit/` - 图标和视觉资源
3. 实现编辑建议的生成、显示和应用的相关逻辑

### Monaco编辑器集成

复刻Monaco编辑器功能：
1. 从原始HTML文件中提取Monaco初始化脚本
2. 实现编辑器配置和主题支持
3. 添加语言服务集成

### 其他功能

对于规则系统、记忆功能、自动修复等功能，按照上述阶段划分，逐步从原始代码中提取和复刻。每个功能点完成后，确保进行充分测试，验证其正常工作。 