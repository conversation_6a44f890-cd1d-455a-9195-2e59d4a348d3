# LazyCode - 智能AI辅助编程工具

LazyCode 是一个强大的 VS Code 扩展，提供智能的AI辅助编程功能，帮助开发者提高编程效率和代码质量。

## 功能特性

### 🚀 主要功能

- **智能编辑建议**: 基于上下文的下一步编辑建议
- **Monaco编辑器集成**: 内置强大的Monaco编辑器
- **代码规则管理**: 自定义代码规则和自动应用
- **代码记忆系统**: 保存和重用代码片段
- **智能代码修复**: 自动检测和修复代码问题
- **差异比较**: 强大的代码差异比较功能

### 📋 详细功能

#### 1. 编辑建议
- 智能分析当前代码上下文
- 提供下一步编辑建议
- 支持多种编程语言
- 可撤销的编辑操作

#### 2. Monaco编辑器
- 完整的Monaco编辑器集成
- 语法高亮和智能提示
- 多语言支持
- 实时预览功能

#### 3. 代码规则
- 创建自定义代码规则
- 支持代码生成、分析、转换规则
- 批量应用规则
- 规则启用/禁用管理

#### 4. 代码记忆
- 捕获代码片段到记忆库
- 按类型和标签组织记忆
- 快速搜索和应用记忆
- 使用统计和星标功能

#### 5. 代码修复
- 自动检测语法错误
- 智能修复建议
- 支持多种编程语言
- 批量修复功能

#### 6. 差异比较
- 文件间差异比较
- 与剪贴板内容比较
- 版本间差异查看
- 可视化差异展示

## 安装和使用

### 安装
1. 在 VS Code 中打开扩展市场
2. 搜索 "LazyCode"
3. 点击安装

### 使用方法

#### 快速开始
1. 安装扩展后，在活动栏中会出现 LazyCode 图标
2. 点击图标打开 LazyCode 侧边栏
3. 使用各种面板功能

#### 命令面板
按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (macOS) 打开命令面板，输入 "LazyCode" 查看所有可用命令：

- `LazyCode: 显示主面板` - 打开主控制面板
- `LazyCode: 显示下一步编辑建议` - 获取编辑建议
- `LazyCode: 打开Monaco编辑器` - 打开内置编辑器
- `LazyCode: 显示代码规则` - 管理代码规则
- `LazyCode: 显示代码记忆` - 管理代码记忆
- `LazyCode: 显示代码修复面板` - 查看和修复代码问题
- `LazyCode: 显示差异比较面板` - 比较代码差异

#### 右键菜单
在编辑器中右键可以快速访问 LazyCode 功能：
- 显示编辑建议
- 应用代码规则
- 捕获代码记忆
- 诊断代码问题
- 比较代码差异

## 配置选项

LazyCode 提供以下配置选项：

- `lazycode.enableNextEditSuggestions`: 启用下一步编辑建议功能 (默认: true)
- `lazycode.enableEmptyFileHint`: 在打开空文件时显示提示 (默认: true)
- `lazycode.monacoEditorTheme`: Monaco编辑器主题 (默认: "vs-dark")
- `lazycode.rulesDirectory`: 代码规则目录的相对路径 (默认: ".cursor/rules")
- `lazycode.memoriesDirectory`: 代码记忆目录的相对路径 (默认: ".lazycode/memories")

## 开发和调试

### 本地开发
1. 克隆项目
2. 安装依赖: `pnpm install`
3. 编译项目: `pnpm run compile`
4. 按 F5 启动调试

### 构建
- 开发构建: `pnpm run compile`
- 生产构建: `pnpm run package`
- 监听模式: `pnpm run watch`

### 测试
- 运行测试: `pnpm run test`
- 编译测试: `pnpm run compile-tests`

## 系统要求

- VS Code 版本 1.100.0 或更高
- Node.js 18.15.0 或更高

## 已知问题

目前没有已知的重大问题。如果遇到问题，请在 GitHub 上提交 issue。

## 更新日志

### 0.0.1
- 初始版本发布
- 实现基础的AI辅助编程功能
- 支持编辑建议、代码规则、记忆系统等核心功能

## 贡献

欢迎贡献代码！

## 许可证

本项目采用 MIT 许可证。

---

**享受智能编程的乐趣！** 🎉
