{"name": "lazycode", "displayName": "LazyCode", "description": "LazyCode - 智能AI辅助编程工具", "version": "0.0.1", "engines": {"vscode": "^1.100.0", "node": ">= 18.15.0"}, "categories": ["AI", "Programming Languages", "Snippets", "Other"], "activationEvents": ["*"], "icon": "media/icon.png", "main": "./dist/extension.js", "contributes": {"commands": [{"command": "lazycode.helloWorld", "title": "Hello World"}, {"command": "lazycode.showMainPanel", "title": "显示主面板", "category": "LazyCode"}, {"command": "lazycode.showNextEdit", "title": "显示下一步编辑建议", "category": "LazyCode"}, {"command": "lazycode.refreshSuggestions", "title": "刷新编辑建议", "category": "LazyCode"}, {"command": "lazycode.undoLastEdit", "title": "撤销上一次编辑", "category": "LazyCode"}, {"command": "lazycode.showMonacoEditor", "title": "打开Monaco编辑器", "category": "LazyCode"}, {"command": "lazycode.loadFromActiveEditor", "title": "从活动编辑器加载内容", "category": "LazyCode"}, {"command": "lazycode.showRulesPanel", "title": "显示代码规则", "category": "LazyCode"}, {"command": "lazycode.createRule", "title": "创建新规则", "category": "LazyCode"}, {"command": "lazycode.refreshRules", "title": "刷新规则", "category": "LazyCode"}, {"command": "lazycode.applyRule", "title": "应用规则", "category": "LazyCode"}, {"command": "lazycode.applyAllRules", "title": "应用所有规则", "category": "LazyCode"}, {"command": "lazycode.showMemoryPanel", "title": "显示代码记忆", "category": "LazyCode"}, {"command": "lazycode.captureMemory", "title": "捕获当前代码到记忆", "category": "LazyCode"}, {"command": "lazycode.applyMemory", "title": "应用记忆", "category": "LazyCode"}, {"command": "lazycode.showFixPanel", "title": "显示代码修复面板", "category": "LazyCode"}, {"command": "lazycode.diagnoseFile", "title": "诊断当前文件", "category": "LazyCode"}, {"command": "lazycode.fixAllIssues", "title": "修复所有问题", "category": "LazyCode"}, {"command": "lazycode.fixSelectedIssues", "title": "修复选中区域的问题", "category": "LazyCode"}, {"command": "lazycode.showDiffPanel", "title": "显示差异比较面板", "category": "LazyCode"}, {"command": "lazycode.compareWithClipboard", "title": "与剪贴板比较", "category": "LazyCode"}, {"command": "lazycode.compareWithPrevious", "title": "与上一版本比较", "category": "LazyCode"}, {"command": "lazycode.compareFiles", "title": "比较两个文件", "category": "LazyCode"}], "viewsContainers": {"activitybar": [{"id": "lazycode-sidebar", "title": "LazyCode", "icon": "media/activitybar.svg"}]}, "views": {"lazycode-sidebar": [{"id": "lazycode.mainView", "name": "LazyCode"}, {"id": "lazycode.nextEditView", "name": "编辑建议"}, {"id": "lazycode.rulesView", "name": "代码规则"}, {"id": "lazycode.memoryView", "name": "代码记忆"}, {"id": "lazycode.fixView", "name": "代码修复"}, {"id": "lazycode.diffView", "name": "差异比较"}]}, "configuration": [{"title": "LazyCode", "properties": {"lazycode.enableNextEditSuggestions": {"type": "boolean", "default": true, "description": "启用下一步编辑建议功能"}, "lazycode.enableEmptyFileHint": {"type": "boolean", "default": true, "description": "在打开空文件时显示提示"}, "lazycode.monacoEditorTheme": {"type": "string", "enum": ["vs", "vs-dark", "hc-black"], "default": "vs-dark", "description": "Monaco编辑器主题"}, "lazycode.rulesDirectory": {"type": "string", "default": ".cursor/rules", "description": "代码规则目录的相对路径"}, "lazycode.memoriesDirectory": {"type": "string", "default": ".lazycode/memories", "description": "代码记忆目录的相对路径"}}}], "menus": {"editor/context": [{"command": "lazycode.showNextEdit", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.showMonacoEditor", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.applyRule", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.applyAllRules", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.captureMemory", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.applyMemory", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.diagnoseFile", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.fixAllIssues", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.fixSelectedIssues", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.compareWithClipboard", "group": "z_commands", "when": "editorTextFocus"}, {"command": "lazycode.compareWithPrevious", "group": "z_commands", "when": "editorTextFocus"}], "view/title": [{"command": "lazycode.showMainPanel", "when": "view == lazycode.mainView", "group": "navigation"}, {"command": "lazycode.refreshSuggestions", "when": "view == lazycode.nextEditView", "group": "navigation"}, {"command": "lazycode.createRule", "when": "view == lazycode.rulesView", "group": "navigation"}, {"command": "lazycode.applyRule", "when": "view == lazycode.rulesView", "group": "navigation"}, {"command": "lazycode.showMemoryPanel", "when": "view == lazycode.memoryView", "group": "navigation"}, {"command": "lazycode.captureMemory", "when": "view == lazycode.memoryView", "group": "navigation"}, {"command": "lazycode.showFixPanel", "when": "view == lazycode.fixView", "group": "navigation"}, {"command": "lazycode.diagnoseFile", "when": "view == lazycode.fixView", "group": "navigation"}, {"command": "lazycode.showDiffPanel", "when": "view == lazycode.diffView", "group": "navigation"}, {"command": "lazycode.compareFiles", "when": "view == lazycode.diffView", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.100.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "typescript": "^5.8.3"}, "dependencies": {"@types/diff": "^8.0.0", "diff": "^8.0.1"}}